namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 数据轮询服务
/// </summary>
public class DataPollingService : IDisposable
{
    private readonly MrpcCommunicationService _communicationService;
    private readonly Timer _pollingTimer;
    private readonly List<string> _devicePaths;
    private readonly object _lockObject = new object();
    private bool _isPolling;

    public event Action<string, object> DataReceived;
    public event Action<Exception> ErrorOccurred;

    public DataPollingService(MrpcCommunicationService communicationService)
    {
        _communicationService = communicationService;
        _devicePaths = new List<string>();
        _pollingTimer = new Timer(PollingCallback, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// 添加轮询设备
    /// </summary>
    public void AddDevice(string devicePath)
    {
        lock (_lockObject)
        {
            if (!_devicePaths.Contains(devicePath))
            {
                _devicePaths.Add(devicePath);
            }
        }
    }

    /// <summary>
    /// 移除轮询设备
    /// </summary>
    public void RemoveDevice(string devicePath)
    {
        lock (_lockObject)
        {
            _devicePaths.Remove(devicePath);
        }
    }

    /// <summary>
    /// 开始轮询
    /// </summary>
    public void StartPolling(int intervalMs = 1000)
    {
        lock (_lockObject)
        {
            if (!_isPolling)
            {
                _isPolling = true;
                _pollingTimer.Change(0, intervalMs);
            }
        }
    }

    /// <summary>
    /// 停止轮询
    /// </summary>
    public void StopPolling()
    {
        lock (_lockObject)
        {
            if (_isPolling)
            {
                _isPolling = false;
                _pollingTimer.Change(Timeout.Infinite, Timeout.Infinite);
            }
        }
    }

    private async void PollingCallback(object state)
    {
        if (!_communicationService.IsConnected) return;

        List<string> devicePathsCopy;
        lock (_lockObject)
        {
            devicePathsCopy = new List<string>(_devicePaths);
        }

        var tasks = devicePathsCopy.Select(async devicePath =>
        {
            try
            {
                var data = await _communicationService.ReadDeviceDataAsync<object>(devicePath);
                DataReceived?.Invoke(devicePath, data);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(ex);
            }
        });

        await Task.WhenAll(tasks);
    }

    public void Dispose()
    {
        StopPolling();
        _pollingTimer?.Dispose();
    }
}