namespace IOTPlatform.SCADA.Models;

public class DeviceInfo : BindableBase
{
    private string _name;
    private string _path;
    private bool _isOnline;
    private double _value;
    private DateTime _lastUpdateTime;
    private string _status;

    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    public string Path
    {
        get => _path;
        set => SetProperty(ref _path, value);
    }

    public bool IsOnline
    {
        get => _isOnline;
        set => SetProperty(ref _isOnline, value);
    }

    public double Value
    {
        get => _value;
        set => SetProperty(ref _value, value);
    }

    public DateTime LastUpdateTime
    {
        get => _lastUpdateTime;
        set => SetProperty(ref _lastUpdateTime, value);
    }

    public string Status
    {
        get => _status;
        set => SetProperty(ref _status, value);
    }
}