using InfluxDB.Client.Core;

namespace IOTPlatform.SCADA.Models;

/// <summary>
/// IoT设备数据点
/// </summary>
[Measurement("device_data")]
public class IoTDataPoint
{
    /// <summary>
    /// 设备ID（标签）
    /// </summary>
    [Column("device_id", IsTag = true)]
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// 设备名称（标签）
    /// </summary>
    [Column("device_name", IsTag = true)]
    public string DeviceName { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型（标签）
    /// </summary>
    [Column("device_type", IsTag = true)]
    public string DeviceType { get; set; } = string.Empty;

    /// <summary>
    /// 位置信息（标签）
    /// </summary>
    [Column("location", IsTag = true)]
    public string Location { get; set; } = string.Empty;

    /// <summary>
    /// 数据值（字段）
    /// </summary>
    [Column("value")]
    public double Value { get; set; }

    /// <summary>
    /// 数据质量（字段）
    /// </summary>
    [Column("quality")]
    public int Quality { get; set; } = 100;

    /// <summary>
    /// 设备状态（字段）
    /// </summary>
    [Column("status")]
    public string Status { get; set; } = "Online";

    /// <summary>
    /// 单位（字段）
    /// </summary>
    [Column("unit")]
    public string Unit { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    [Column(IsTimestamp = true)]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建数据点
    /// </summary>
    public static IoTDataPoint Create(string deviceId, string deviceName, double value, 
        string deviceType = "", string location = "", string unit = "", int quality = 100)
    {
        return new IoTDataPoint
        {
            DeviceId = deviceId,
            DeviceName = deviceName,
            DeviceType = deviceType,
            Location = location,
            Value = value,
            Unit = unit,
            Quality = quality,
            Status = "Online",
            Timestamp = DateTime.UtcNow
        };
    }
}

/// <summary>
/// 设备数据记录（用于查询结果）
/// </summary>
public class DeviceDataRecord
{
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public double Value { get; set; }
    public int Quality { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}
