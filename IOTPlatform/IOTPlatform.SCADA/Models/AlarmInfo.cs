using Prism.Mvvm;

namespace IOTPlatform.SCADA.Models;

public class AlarmInfo : BindableBase
{
    private string _id;
    private string _deviceId;
    private string _deviceName;
    private string _message;
    private AlarmLevel _level;
    private DateTime _timestamp;
    private bool _isAcknowledged;
    private string _acknowledgedBy;
    private DateTime? _acknowledgedTime;

    public string Id
    {
        get => _id;
        set => SetProperty(ref _id, value);
    }

    public string DeviceId
    {
        get => _deviceId;
        set => SetProperty(ref _deviceId, value);
    }

    public string DeviceName
    {
        get => _deviceName;
        set => SetProperty(ref _deviceName, value);
    }

    public string Message
    {
        get => _message;
        set => SetProperty(ref _message, value);
    }

    public AlarmLevel Level
    {
        get => _level;
        set => SetProperty(ref _level, value);
    }

    public DateTime Timestamp
    {
        get => _timestamp;
        set => SetProperty(ref _timestamp, value);
    }

    public bool IsAcknowledged
    {
        get => _isAcknowledged;
        set => SetProperty(ref _isAcknowledged, value);
    }

    public string AcknowledgedBy
    {
        get => _acknowledgedBy;
        set => SetProperty(ref _acknowledgedBy, value);
    }

    public DateTime? AcknowledgedTime
    {
        get => _acknowledgedTime;
        set => SetProperty(ref _acknowledgedTime, value);
    }
}

public enum AlarmLevel
{
    Info = 0,
    Warning = 1,
    Critical = 2,
    Emergency = 3
}

public class AlarmStatistics : BindableBase
{
    private int _totalCount;
    private int _infoCount;
    private int _warningCount;
    private int _criticalCount;
    private int _emergencyCount;

    public int TotalCount
    {
        get => _totalCount;
        set => SetProperty(ref _totalCount, value);
    }

    public int InfoCount
    {
        get => _infoCount;
        set => SetProperty(ref _infoCount, value);
    }

    public int WarningCount
    {
        get => _warningCount;
        set => SetProperty(ref _warningCount, value);
    }

    public int CriticalCount
    {
        get => _criticalCount;
        set => SetProperty(ref _criticalCount, value);
    }

    public int EmergencyCount
    {
        get => _emergencyCount;
        set => SetProperty(ref _emergencyCount, value);
    }
}
