namespace IOTPlatform.SCADA.Models;

/// <summary>
/// InfluxDB 2.x 连接配置
/// </summary>
public class InfluxDbConfiguration
{
    /// <summary>
    /// InfluxDB服务器URL
    /// </summary>
    public string Url { get; set; } = "http://localhost:8086";

    /// <summary>
    /// 认证令牌
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// 组织名称
    /// </summary>
    public string Organization { get; set; } = "iot-platform";

    /// <summary>
    /// 数据桶名称
    /// </summary>
    public string Bucket { get; set; } = "iot-data";

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 批量写入大小
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// 批量写入间隔（毫秒）
    /// </summary>
    public int FlushIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(Url) &&
               !string.IsNullOrWhiteSpace(Token) &&
               !string.IsNullOrWhiteSpace(Organization) &&
               !string.IsNullOrWhiteSpace(Bucket);
    }
}
