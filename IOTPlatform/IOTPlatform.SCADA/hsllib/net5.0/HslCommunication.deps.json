{"runtimeTarget": {"name": ".NETStandard,Version=v2.1/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.1": {}, ".NETStandard,Version=v2.1/": {"HslCommunication/********": {"dependencies": {"Newtonsoft.Json": "13.0.1", "System.IO.Ports": "6.0.0"}, "runtime": {"HslCommunication.dll": {}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/6.0.0": {}, "runtime.linux-arm64.runtime.native.System.IO.Ports/6.0.0": {}, "runtime.linux-x64.runtime.native.System.IO.Ports/6.0.0": {}, "runtime.native.System.IO.Ports/6.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "6.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "6.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "6.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "6.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "6.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/6.0.0": {}, "runtime.osx-x64.runtime.native.System.IO.Ports/6.0.0": {}, "System.Buffers/4.5.1": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.6.28619.1"}}}, "System.IO.Ports/6.0.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Memory": "4.5.4", "runtime.native.System.IO.Ports": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.IO.Ports.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Memory/4.5.4": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.Numerics.Vectors/4.4.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Principal.Windows/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}}}, "libraries": {"HslCommunication/********": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-75q52H7CSpgIoIDwXb9o833EvBZIXJ0mdPhz1E6jSisEXUBlSCPalC29cj3EXsjpuDwr0dj1LRXZepIQH/oL4Q==", "path": "runtime.linux-arm.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xn2bMThmXr3CsvOYmS8ex2Yz1xo+kcnhVg2iVhS9PlmqjZPAkrEo/I40wjrBZH/tU4kvH0s1AE8opAvQ3KIS8g==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-16nbNXwv0sC+gLGIuecri0skjuh6R1maIJggsaNP7MQBcbVcEfWFUOkEnsnvoLEjy0XerfibuRptfQ8AmdIcWA==", "path": "runtime.linux-x64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KaaXlpOcuZjMdmyF5wzzx3b+PRKIzt6A5Ax9dKenPDQbVJAFpev+casD0BIig1pBcbs3zx7CqWemzUJKAeHdSQ==", "path": "runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fXG12NodG1QrCdoaeSQ1gVnk/koi4WYY4jZtarMkZeQMyReBm1nZlSRoPnUjLr2ZR36TiMjpcGnQfxymieUe7w==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/As+zPY49+dSUXkh+fTUbyPhqrdGN//evLxo4Vue88pfh1BHZgF7q4kMblTkxYvwR6Vi03zSYxysSFktO8/SDQ==", "path": "runtime.osx-x64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dRyGI7fUESar5ZLIpiBOaaNLW7YyOBGftjj5Of+xcduC/Rjl7RjhEnWDvvNBmHuF3d0tdXoqdVI/yrVA8f00XA==", "path": "system.io.ports/6.0.0", "hashPath": "system.io.ports.6.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Numerics.Vectors/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "path": "system.numerics.vectors/4.4.0", "hashPath": "system.numerics.vectors.4.4.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3TIsJhD1EiiT0w2CcDMN/iSSwnNnsrnbzeVHSKkaEgV85txMprmuO+Yq2AdSbeVGcg28pdNDTPK87tJhX7VFHw==", "path": "system.runtime.compilerservices.unsafe/4.5.3", "hashPath": "system.runtime.compilerservices.unsafe.4.5.3.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}}}