using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace IOTPlatform.SCADA.Converter;

#region 值转换器

    /// <summary>
    /// 连接状态颜色转换器
    /// </summary>
    public class StatusColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "已连接" => new SolidColorBrush(Color.FromRgb(163, 190, 140)), // 绿色
                    "连接中..." => new SolidColorBrush(Color.FromRgb(235, 203, 139)), // 黄色
                    "未连接" => new SolidColorBrush(Color.FromRgb(191, 97, 106)), // 红色
                    _ => new SolidColorBrush(Color.FromRgb(129, 161, 193)) // 默认蓝色
                };
            }
            return new SolidColorBrush(Color.FromRgb(129, 161, 193));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 在线状态转换器
    /// </summary>
    public class OnlineStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isOnline)
            {
                return isOnline 
                    ? new SolidColorBrush(Color.FromRgb(163, 190, 140)) // 绿色
                    : new SolidColorBrush(Color.FromRgb(191, 97, 106)); // 红色
            }
            return new SolidColorBrush(Color.FromRgb(191, 97, 106));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 报警等级颜色转换器
    /// </summary>
    public class AlarmLevelColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int level)
            {
                return level switch
                {
                    1 => new SolidColorBrush(Color.FromRgb(136, 192, 208)), // 信息-蓝色
                    2 => new SolidColorBrush(Color.FromRgb(235, 203, 139)), // 警告-黄色
                    3 => new SolidColorBrush(Color.FromRgb(208, 135, 112)), // 严重-橙色
                    4 => new SolidColorBrush(Color.FromRgb(191, 97, 106)), // 紧急-红色
                    _ => new SolidColorBrush(Color.FromRgb(129, 161, 193)) // 默认
                };
            }
            return new SolidColorBrush(Color.FromRgb(129, 161, 193));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    #endregion
