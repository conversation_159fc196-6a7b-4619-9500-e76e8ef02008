边缘网关系统客户端

文件b 文件日 视图(V 工具① 关于A) 更新服务器 启动测试的Mqt服务器Edge:DD57F968D9FE ·。c

服务器列表 中 设备监视界面×  
+c 设备名称：Edge:DD57F968D9FE 开始运行时间：2023-11-1221:20:46 Version: 1.7.2 \$4904①2×45  
Edge:DD57F968D9FE [127.0.0.1] 设备7报警5工厂 设备名称：阿里云测试plc 设备名称：腾讯云西门子 设备名称：欧姆龙PLC-FinsTcp车间 活动时间：2023-11-12 21:23:29 活动时间：2023-11-1221:23:31 活动时间：2023-11-12 21:20:46二 采集耗：610ms 失败次数：0 采集耗时：36ms 失败次数：1 采集耗时：0ms 失败次数：0 停用三菱PLC-FxSerial 02 ×2 xo 0。 ×1 xo 0。 A0 ×。PcFxSeral-OverTcp ：121204 ① 设询：11212046 ：212104 ①三美PLC-A3C-Overcp 采集耗时：2098 ms 2.8分钟 采集：0ms 2.8分钟 采集时：6136ms 2.8分钟失败次数：23 失败次数：0 失败次数：27-Pan 松下PLC-Mewtocol Call Method[方法数据名称] failed:连接192.168.6.. 当前设备绑定的插件不存在，设备无法请求 Request[没有的数据] failed:连接192.168.0.109:5..-Pan 松下PLC-Mewtocol-OverTcp由-SIE 西门子设备 数据信息设备信息方法接口报警信息OEE信息离线信息设备日志u ModbusTcp M ModbusUdp 节点名称： 工厂-/车间二/腾讯云西门子Med ModbusRtu 数据名称 值(Value) Unit 类型 权限 描述Mud ModbusAscii s 门PLC-OverTC 速度 温度 1382.4 0 m/s Double Float ReadWrite ReadWrite 这个是速度，单位是m/sSIE 西门子PLC-PPI Alarm False Bool ReadWrite-SIE 西门子PLC-PPI-OverTcp CC ( "B':100, “速度': 100.0, “温度':0.0, "Alarm": false} Struct Readom 欧姆龙PLC-FinsTcp DD [\*B':0, "速度：0.0, “温度：0.0, "Alarm': false).. Struct| Read-om 欧姆龙PLC-FinsUdp 订货号 6ES7 215-1AG40-0XB0 String Read 次方法的调用信息由-om 欧姆龙PLC-HostLink 测试byte 0 Byte ReadWrite 一次完整的数据请求om 欧姆龙PLC-HostLink-OverTcp BCD测试 001135 BCD ReadWrite 一次完整的数据请求龙LHsio BCD数组测试 [【"010000","000300","000400","000600"] BCD ReadWrite 一次完整的数据请求基恩士PLC-MC Bool测试位 False Bool ReadWrite 一次完整的数据请求基恩士PLC-Nano 写入脚本值 61 Double ReadWrite 一次完整的数据请求①7 其因+DIC-Nane-Overicn

# 边缘网关系统用户手册

# 可配，灵活，易扩展的采集分析系统 V1.8.0

基于 HslCommunication 组件通信，将工业软件，上位机系统，mes 系统中经常重复开发的采集软件进行功能剥离，形成边缘网关，支持基本的数据分析，结构复用支持方法接口调用，支持插件扩展，插件用于构建标准化的设备采集让你的设备以标准的接口，可升级的插件，快速部署对外开放数据

杭州胡工物联科技有限公司***********************：200962190，1759967761微信：13516702732

# 前言

前言说明： 当前的文档主要是针对如何使用边缘网关软件进行简单的说明，方便大家上手操作。用户配置网关的客户端软件的运行至少需要.NET Framework4.6.1 以上框架支持，WIN10 系统默认支持，不支持XP 系统，WIN7 系统需要安装相应的框架。如果没有安装的话，可以参考下面的地址，可以下载到各个版本（本手册部分截图样式和最新版有出入但不影响功能使用，感谢理解）：

https://dotnet.microsoft.com/download/dotnet-framework

# Supported versions

<html><body><table><tr><td>Version</td><td> Release date</td><td> End of support</td></tr><tr><td>NET Framework 4.8 (recommended)</td><td>April 18, 2019</td><td></td></tr><tr><td>.NET Framework 4.7.2</td><td>April 30, 2018</td><td></td></tr><tr><td>NET Framework 4.7.1</td><td>October 17, 2017</td><td></td></tr><tr><td>NET Framework 4.7</td><td>April 05, 2017</td><td></td></tr><tr><td>NET Framework 4.6.2</td><td>August 02, 2016</td><td></td></tr><tr><td>.NET Framework 4.6.1</td><td>November 30, 2015</td><td>April 26, 2022</td></tr><tr><td>.NET Framework 4.6</td><td>July 20, 2015</td><td>April 26, 2022</td></tr><tr><td>.NET Framework 4.5.2</td><td>May 05, 2014</td><td>April 26, 2022</td></tr><tr><td>NET Framework 3.5 SP1</td><td>November 18, 2008</td><td>October 10, 2028</td></tr></table></body></html>

# Out of support versions

followingreleases havereached endoflife,meaning they'enolongersupported.Werecommend moving toasupportedrelease

<html><body><table><tr><td>Version</td><td>Release date End of support</td></tr><tr><td>.NET Framework 4.5.1</td><td>January 12, 2016</td></tr><tr><td>.NET Framework 4.5</td><td>January 12, 2016</td></tr><tr><td>.NET Framework 4.0</td><td>January 12, 2016</td></tr></table></body></html>

本手册文档将会不定期更新，新版会随着软件一起发布，请关注新版本的发布信息，恕不另行通知。

# 隐私条款：

胡工物联尊重并保护所有使用边缘网关产品用户的个人隐私权。为了给您提供更准确、更有个性化的服务， 胡工物联会按照本隐私权政策的规定使用和披露您的个人信息。除本隐私权政策另有规定外，在未征得您事先许可的情况下，胡工物联不会将这些信息对外披露或向第三方提供。您在同意边缘网关产品服务使用协议之时，即视为您已经同意本隐私权政策全部内容。

本隐私权政策属于边缘网关服务使用协议不可分割的一部分。

# 版权声明：

本软件著作权归胡工物联科技有限公司所有，软著登记号：2021SR1315022，禁止破解和任何的逆向工程，否则将负法律责任。未经授权，任何人不得公开分享网关系统的源代码，否则胡工物联有权向法院提起诉讼。

# 前言

一、系统简述 .

1.1 软件介绍 .. 5  
1.2 运行环境 . 5  
二、服务器安装说明. . 5  
2.1 Windows 服务端安装及启动 . . 5  
2.2 Ubuntu 系统运行服务器.. .... 7  
三、基本功能说明 ... . 9  
3.1 客户端安装及启动 .. . 9  
3.2 初始化连接操作 . 9  
3.3 添加采集数据 . . 13  
3.4 新增数据解析 ... .. 16  
3.5 结构体的配置 .. ..... 17  
3.6 报警信息配置 . . 22  
3.7 OEE 信息配置 . . 28  
3.8 数据库请求配置 . . 30  
3.9 下载配置到网关 . . 34  
3.10 查看网关数据 . ... 35  
3.11 查看设备数据 .. . 37  
3.12 暂停继续采集操作 .. ... 39  
3.13 设备数据写入操作 ... .... 42  
3.14 定时写入请求 . .... 45  
3.15 定时方法请求 . ...... 51  
3.16 请求分类及多个请求同步 . . 54  
3.17 请求的附加条件 .. . 56  
3.18 网关账户设置 . . 57  
3.19 网关日志设置 . . 59  
3.20 切换 DTU 模式 . . 60  
3.21 主备模式 .. .. 64  
3.22 更新服务器程序 .. .. 65  
3.23 网关备用配置文件 .. . 66  
3.24 节点复制粘贴 . 68  
3.25 串行端口映射 71  
3.26 统一查看异常设备列表 . 73  
四、管道说明 ..... .. 74  
4.1 串口网口管道 74  
4.2 单线程管道 . .. 78  
五、对外数据接口 . . 79  
5.1 基于 MRPC 协议 . . 79  
5.2 使用代码来读写数据 .. .. 83  
5.3 浏览器访问数据 . .. 85  
5.4 基于 MQTT 的数据订阅及发布 ... .. 87  
5.5 数据上传到 Redis .. ....... 91  
5.6 数据发布到远程 MQTT 及写入 .. . 92  
5.7 数据和物联网平台 JetLinks 对接 . 94  
5.8 设备启用 MRPC 服务 . .. 98  
5.9 设备启用 WebApi 服务 . . 99  
5.10 Websocket 推送及写入功能 . . 101  
六、设备模板 . ... 104  
6.1 添加一个设备模板 .. . 104  
6.2 添加一个基于模板的设备 .. . 105  
七、插件扩展 ... . 107  
7.1 扩展新设备实现. 107  
7.2 用于标准化设备数据 .. ... 111  
7.3 基于标准模板的插件 . . 115  
7.4 扩展网关现有设备协议功能 ... 118  
7.5 插件的安装和卸载 ... .. 119  
八、使用 Node-Red 扩展 .. .... 122  
8.1 Node-Red 基本说明和使用 . . 122  
8.2 保存报警信息到数据库 . .. 126  
九、高级应用 . 129  
9.1 构建 Modbus 总站 .. . 129  
9.2 查看设备的通信报文 ... . 134  
9.3 串口及网口转 MQTT 135  
9.4 自由的网口及串口通信 ... .... 139  
9.5 设备分身(富设备使用) . . 146

十、最后总结 . 151

# 一、 系统简述

# 1.1 软件介绍

本系统由服务端和客户端组合而成，使用客户端软件进行连接服务器之后，就可以配置服务器需要采集数据的设备，可以从三菱plc，台达plc，永宏 plc，富士 plc，通用电气plc，基恩士 plc，modbus 仪器仪表plc，欧姆龙plc，松下plc，西门子plc，横河plc，Redis 数据库，以及服务器端安装的第三方插件设备等进行数据采集，服务器端的设备支持 DTU 模式，可以从远程服务器访问本地的网口或是串口设备，客户端支持查看服务器的所以的设备采集状态，运行状态，数据信息，报警信息，设备稼动率数据，以及网关本身的运行状态参数。

# 1.2 运行环境

本系统主要分为服务器端软件和客户端软件，两者的运行环境不一致，对于服务器端软件，支持运行在 Linux 系统（Alpine3.11 以上，CentOS7 以上，Debian9 以上，Fedora32 以上，Red Hat7以上，Ubuntu1804 等），Windows 7/8/8.1/10/11 系统。

对于客户端软件，目前只支持运行在 windows 系统上，最低的硬件支持：

a) CPU： PIII 800 以上  
b) 内存：512MB 以上  
c) 硬盘：40GB 以上  
d) 网络：TCP/IP 局域网络或拨号网络  
e) 串行接口：如果需要支持访问串口设备，则需要串口支持  
软件环境：  
a） 系统：windows7, windows8, windows8.1, windows10, windows11  
b） 框架：.NET Framework4.6.1 以上框架

# 二、 服务器安装说明

# 2.1 Windows 服务端安装及启动

边缘网关主要分成2 个软件，一个是用于数据采集分析的服务器（按照运行平台的差异分了4 个版本，分别是 windows 版本，linux-arm 版本，linux-arm64,  linux-x64 版本），另一个是可视化的用于配置服务端各种采集的边缘网关客户端。

如下所示：

A 名称 修改日期 类型HslTechnology.EdgeServer-linux-arm.zipHslTechnology.EdgeServer-linux-x64.zipHslTechnology.EdgeServer-Win.zipHslTechnology.EdgeViewer.zip边缘网关使用手册.pdf

我们先在 windows 系统运行第一个边缘服务器，解压 HslTechnology.EdgeServer-Win.zip 之后不是双击运行，需要右键管理员运行（如果该程序已经配置为管理员启动的话，则直接双击启动即可）。运行之后

E:\HslTechnologyEdge\HslTechnology.EdgeServer.exe □ × 2021-08-16 21:23:22.597 Thread [001]   
[001] 购 會 件：:chlgEdgedvics.xml [001] 息： Microsoft Windows NT 6.2.9200.0 [001] 的CLR 4.0.30319.42000 [001] DESKTOP-T8E9E55 [001] [001] 加载配置 完成， 台 意 2021-08-16 21:23:22.820 Thread [001] MqttServer[521] 2021-08-1621232a [001] tServic522服器 wait for connections   
信息   
言 息 2021-08-16 21:23:22.830 Thread [001] EdgeServices 远程更新服务器加载成功！   
信息 2021-08-1621:23:22.830 Thread [001] Server Start!

# 命令行启动：

本程序支持命令行启动，可以很方便的从其他的程序使用 cmd 脚本来启动当前的程序，并且可以动态指定设备的配置文件的路径，参考下面

管理员:命令提示符- E\HslTechnologyEdge\HslTechnology.EdgeServer.exe □ ×  
Microsoft Windows[版本10.0.19043.1165]  
(c)Microsoft Corporation。保留所有权利。WINDOWS\system32>E:\Hs1TechnologyEdg Server.exe[001 加载配置文备配 件路径：E:\Hs1TechnologyEdgedevices.xml001] 亍的！ 自 E:\Hs1TechnologyEdge\[001] 息：Microsoft WindowsNT6.2.9200.0[001] 行的CLR信息 4.0.30319.420008 2021-08-16 21:21:36.439 Thread [001] 工的 首 E 息 DESKTOP-T8E9E552021-08-16_21:21:36.440 Thread [001] 的程 序 唱 1.0.0息 1-08-1626 [001] 加配置文 完成： 共计 蒼勘引 鑫： 台息 2021-08-1621165Ta [001] HttpServer[522 Web服务器 加载成功 wait for connections[001] Edrervicrs 远程更新服务器加载成功！息 2021-08-16 21:21:38.597 Thread [010] 上传服务器的线程启动！

如果需要动态指定配置文件路径，参考下面的指定：路径后带参数 -xml D:\devices.xml

Microsoft Windows[版本10.0.19043.1165  
(c）Microsoft Corporation。保留所有权利。\WINDOWS\system32>E:\Hs1Techno1ogyEdge\Hs1Te gy.EdgeServer.exe-xml D:\devices.xml言息] 2021-08-1621:33:06.090 Thread [001] 正在 配文件路径：D:devices.m信息] 2021-08-1621:33:06.095 Thread [001] 业  
[信息] 2021-08-16 21:33:06.156 Thread [001] 运 行的自录位置： E:\Hs1TechnologyEdge\j 2021-08-16 21:33:06.156 Thread [001] 运行的系统信息：Microsoft Windows NT 6.2.9200.0原 2021-08-1621:33:06.156Thread [001] 运 行的CLR 4.0.30319.420002021-08-16 21:33:06.156 Thread 001 信息 DESKTOP-T8E9E55

其中-xml 忽略大小写，接下来就是操作客户端的事，当客户端操作之后，服务器只需要进行重启就可以。

# 2.2 Ubuntu 系统运行服务器

我们把 HslTechnology.EdgeServer-linux-x64.Zip 解压之后放到 ubuntu 系统的目录里去，然后使用命令行启动当前的服务器软件，如下所示：sudo/home/<USER>/Documents/linux-x64/HslTechnology.EdgeServer

输入密码之后，就显示下图的信息，这就代表启动成功了。注意，本次发布的程序包是自带.net 6 的环境的，不需要额外安装.net 6 环境。

Terminal Mon 21:47Home Documents linux-x64 Q Recent D M A D 國 DHomeDesktopDowments ￥ C  
日 Music Mirosoft.c  
中 Pictures dathlin@ubuntu: \~ 00Videos File Edit View Search Terminal Help  
D Trash Pugn dddtoOther Locations [Info] 2021-08-16 21:46:15.72 Tead Info] 2021-08-16 21:46:15.890 ThreadInfo] 2021-08-16 21:46:15.890 Thread [001] 运行的系统信息 :Unix 5.4.0.67Info] 2021-08-16 21:46:15.891 Thread [001] 运行的CLR信息：3.1.18Info] 2021-08-16 21:46:15.891 Thread [001] 运行的计算机信息：ubuntu[Info] 2021-08-16 21:46:15.012 Thread Info] 2021 08-16 21:46:16.022 Thread202 -1621:46:16.137Thread [001] MqttServer[521] Start engine

同理，同样可以使用路径后带参数 -xml D:\devices.xml如果运行的时候出现下面错误(通常出现于一些linux 系统，例如下面的截图):

root@armbian:\~/hsl/linux-arm64# sudo HslTechnology.EdgeServer   
sudo:HslTechnology.EdgeServer:command not found   
root@armbian:\~/hsl/linux-arm64# sudo./HslTechnology.EdgeServer   
Processterminated.Couldn't find a valid ICU package installed on the system.Set the configuration flag   
lobalization support. atSystem.Environment.FailFast(System.String) atSystem.Globalization.GlobalizationMode.GetGlobalizationInvariantMode() atSystem.Globalization.GlobalizationMode..cctor() atSystem.TimeZoneInfo.GetDisplayName(TimeZoneDisplayNameType,System.String ByRef) atSystem.TimeZoneInfo..ctor(Byte[],System.String,Boolean) atSystem.TimeZoneInfo.GetTimeZoneFromTzData(Byte[],System.String) atSystem.TimeZoneInfo.GetLocalTimeZoneFromTzFile() atSystem.TimeZoneInfo+CachedData.CreateLocal(） atSystem.DateTime.get_Now() atHslTechnology.Edge.HslTechnologyHelper.GetDateTimeNow(） atHslTechnology.Edge.DataBusiness.Time.EdgeRunTimeAnalysis.AddRuntime() atHslTechnology.Edge.DataBusiness.Time.EdgeRunTimeAnalysis..ctor(System.String) atHslTechnology.EdgeServer.Program+<Main>d_1.MoveNext() atSystemRuntime.CompilerServices.AsyncMethodBuilderCore.Start[HslTechnology.EdgeServer.Program<Main>d   
PublicKeyToken=b8f06054f87a3f67]](<Main>d_1ByRef) atSystemRuntime.CompilerServices.AsyncTaskMethodBuilder.Start[HslTechnology.EdgeServer.ProgramMain>d PublicKeyToken=b8f06054f87a3f67]](<Main>d1ByRef）

可以参考下面的方式尝试修改一个参数：

HslTechnology.EdgeServer.pdb  
回HslTechnology.EdgeServer.runtimeconfig.dev.json  
□ HslTechnology.EdgeServer.runtimeconfig.json  
Microsoft.Bcl.Asynclnterfaces.dll 应用程序扩展  
Micr ? 文件(F) 编辑(E) 选择(S）查看（V) 转到(G) 运行(R) 终端) 帮助（H) HslTechnology.EdgeServer.runtimeco  
Micr { ) HslTechnology.EdgeServer.runtimeconfig.json X  
Micr G:HslPrjects>hcholdeen>Release>etcoeap3.1>{)hdgeevertiofjo>  
Micr O 12 "runtimeoptions":{  
图Mic % 34   
图 Micre > 6 "version": "3.1.0"  
图Mir 品 8   
Micr A 1112  
图 Micre 13 子  
Micr  
Newt

如果提示错误：“no usable version of libssl was found”

可以参考：htps:/blog.csdn.net/m0_3808624/article/details/121512894

# 三、 基本功能说明

# 3.1 客户端安装及启动

客户端的安装比较简单，只要将客户端的压缩包拷贝到电脑系统的任意路径，然后解压即可，双击程序 HslTechnology.EdgeViewer.exe 即可打开主程序界面。

![](images/838bbd82e24727678ad1583f03a7ad084a213b1ccc922df067e478c5bc4b16e3.jpg)

# 3.2 初始化连接操作

本小节介绍连接网关，在主界面的服务器列表侧点击 号即可：

![](images/6a75cdf3a4dcb3b0809be34205d879f8e9eb992cb02312850d42bff54c0ef27d.jpg)

# 比如此处输入本地网关：

![](images/44cb386b31b411fb58cc3ba08af29415bab1e02bd3fbb9551e7487bfa71454ad.jpg)

然后点击”通讯测试”，成功后点击”完成”。左侧的列表显示设备信息，现在边缘网关没有任何的设备内容，所以需要先进行配下需要通信和采集的 PLC 信息。按照如下的操作，右键点击”编辑采集信息”。

![](images/f8ea540f782ebd3c55822fbff6615f34a7ecf786e6b176611b1d2f43aef2f77d.jpg)

点击之后就是显示配置界面信息，在这个界面就可以配置相关的内容。

![](images/5de53d415e3ce406e44b4246a7843af01868eaa99a79e41cfa776e086f25a44c.jpg)

• Devices 节点：用来存放所以采集的 PLC 设备，Modbus 设备等等，支持节点分类归档，也可以直接添加几个PLC。Regular 节点：用来存放一些结构体数据的解析的，加入你的 PLC 存储了一些格式一致的结构体数据，使用本节点的功能，会使得配置加快很多。Alarm 节点：用于报警相关的信息配置，配置报警的内容，等级，用来和 PLC 的节点进行数据绑定操。  
OEE 节点：用于设备一些状态的时间消耗分析，比如配置了有效工作状态，那么可以算出设备的OEE 信息，也就是设备有效工作时间占比。  
• Template 节点：模板节点，用来创建设备的通用模板，通常用于一些采集请求一致的设备。DataBase 节点：数据库资源节点，用于创建数据库的连接操作。  
操作的时候，使用鼠标右键即可进行相关的操作，添加完成相关的设备节点后。设备的节点，可以点

击鼠标左键拖拽到其他的分类下面，请求的节点也是一样的，方便快速挪动操作。

![](images/d72b3e196034d5a1e58f5eab96db9f59e5ddc9b663172d945548c4a4633d3aad.jpg)

比如我添加了三菱的设备，就要设定 PLC 相关的参数，只有参数正确才能确保连接成功，每种PLC的连接信息都不一致，大多数参数的值需要根据实际情况来输入，如果熟悉PLC 的通信机制的话，那么配置起来会得心应手很多。当你选择了某个配置属性后，也会显示出来属性的注释。如下图所示就是显示了”是否二进制”的属性。

![](images/57387bc5af474309c58fe27222bd43d77f69d0ab0a30810ad070d7e19ec4bdb7.jpg)

其中需要注意的是，节点名称在当前分类下是唯一的，不同的路径下可以重叠，对于服务器来说，[路径+设备名称]可以找到唯一的设备对象，然后进行远程调用操作，在客户端远程操作设备的数据及方法接口时，都需要使用这个唯一的设备路径来访问。

# 节点名不得含有 \?/.,<>!#+@等特殊字符。 可以包含空格

下图是带有分类信息配置的一个示例，实际可以根据自身工厂的情况来配置，层级关系可以无限复杂。

![](images/8d4cde35b1c4ac7528c0108bc54633805f0201a13b86bfb80df219d18c142eb0.jpg)

# 3.3 添加采集数据

我们添加了设备的数据信息，接下来就要添加采集的数据，我们现在简单的举个例子，无论是三菱，西门子，欧姆龙，modbus 等等，操作和原理都是一致的。

![](images/1c3cff131c56276d5ded5a0176078998f0ec517b476b173e86d20aab483ebf5c.jpg)

我们可以看到目前版本分为六种基本的数据请求，其中的区别和选择如下：

a) 标量请求：请求单个的short，int，float， double 类型等等数值，或是他们的数组。优点是配置简单，方便支持读写操作，缺点是如果配置的标量请求数量多，效率慢，一次一个标量请求就是一次网络通信。

b) 标量缓存数据：可以向设备添加缓存数据信息，这个数据和 PLC 没有关系，但是可以使用API 接口来对数据进行操作操作，修改的数据信息针对内存而言的。

c) 原始字节请求：一次性读取大量的byte[]数据，然后配置解析操作。优点是数据多的话，读取数据非常快，在配置上会稍微复杂一些，需要很清楚各个数据的原始字节排列，需要完全正确的数据排列，否则会解析错误数据信息。

d) 定时写入请求：配置定时写入数据的请求，可以定时调用请求写入一些静态或是动态的数据写入到网关中，或是 PLC 的点位里面去。

e) 方法调用请求：配置方法接口调用的请求，可以定时调用某个方法信息，支持传入相关的数据参数内容，对于一些特殊的设备，例如机器人，机床设备，目前只能使用方法调用请求来访问设备的数据信息。

f) 数据库调用请求：配置数据库调用的请求，可以定时执行数据库的 SQL 语句，可以从网关中抓取出数据存储指定的数据库里面。

# 此处举例采集一个三菱 PLC 的一个 D0 的short 数据

数据请求节点[设备A01] ×  
地址示例 节点信息 1输入唯一的数据名称  
地址类型 描述信息 位 字 备注 请求名称： 产量 显示别名：  
M0 内部继电器 0 J 请求描述： 一次完整的数据请求 单位：  
x0 输入电器 ： 0 默认16进制，8进制 x011 地址信息 2 输入采集的数据地址  
SMO SM特殊继电器 S J 请求地址： D0 □是否数组  
S0 步进继电器 0 0 数据类型： short ③ 选择实际的类型  
[ 锁存继电器 0 J  
F0 报警器 0 J 字符串相关： 字符串长度：  
vo 边沿继电器 J J  
B0 链接继电器 J . 16进制地址 数据权限： □禁止远程账户写入(包括管理员账户）□启用数据发布订阅 (支持数组)  
SBO 特殊链接继电器 0 J 16进制地址  
DX0 直接输入 J J 请求次数周期  
DYO 直接输出 J J 请求间隔： 1.000 日 (单位：毫秒)  
TS0 定时器触点 0 × 无限次指定次数 4 每秒调用一次，不设置额外的  
TCO 定时器线圈 0 × 附件条件： 情况 不使用附加条件  
SS0 累计定时器触点 0 ×  
sCo 累计定时器线圈 0 × 数据变换  
cso 计数器触点 0 × 无变换○值变换(结果double) ○取反(bool)○表达式(只读)  
cco 计数器线圈 0 ×x0 y= 1 \* [value] + 0  
DO 数据寄存器  
SDO 特殊数据寄存器 X 0 □强制小数点位数？ 5 根据实际情况确认是否变换  
wo 链接寄存器 × 0 附加功能  
SWO 特接存器 X ： □报警分析  
Z0 变址寄存器 × 0 □Oee分析  
ZRO ZR文件寄存器 × 0  
TNO 定时器当前值 × J 6 点击确认 确认 取消

其中请求名称也是数据的名称，要求设备唯一，显示别名默认为空，如果输入了其他本文，则在客户端上显示的则为别名，但是在配置的界面上，仍然显示请求名称信息，当我们添加之后，数据就会出现在菜单的左侧。

![](images/944065f38f9aa5c29625ec648e7dfd0032e5e955fc8800004f127ed94676d9ff.jpg)

我们来看看新增原始字节读取，为什么需要这个呢？因为它可以实现更加高效的采集，当连续的地址里包含了不同类型的数据后，使用标量请求是不能一个标签实现采集的，我们可以采集连续地址的原始字节数据，然后配置解析操作。假设我们采集D0-D99 的原始数据，然后解析出相关的数据信息。第一步先配置采集的数据。

![](images/6812ee32c1432605f8f0da0efae9195dd0b6d4f6bcb9f66411a09130925c86eb.jpg)

当我们再点击原始数据节点的时候，这时候，右侧会显示 200 个字节的数据解析，当然，默认是什么都没有的，所以需要我们来配置数据解析。如下图所示（还支持数据及时监视）：

![](images/eeb6501f67b18b34215f98c9cfff8e5d9178b0c6de499990edd0ad9b7b0dd2f8.jpg)  
边缘网关系统客户端

# 3.4 新增数据解析

在上一小节中，我们配置采集了原始数据，但是还没有任何的数据解析操作，本小节介绍对采集的数据进行自定义的解析，我们操作D10 是一个 short 类型数据，那么按照三菱的一个地址表示 2 个字节的规律，我们配置的字节索引就是 20，如下配置：

![](images/b14f7aea9227308feb8ee29bcf3e5b51dfeb59f25299543481438a24d93839a9.jpg)

点击确认之后，相关位置的数据信息就会显示数据，我们看到正好是第 20 个字节索引上，解析了一个short 类型的数据，字节的上方显示类型名称，下方显示变量名称：

边缘网关系统客户端 □ ×  
文件(F)视图(V）工具(T) 关于(A) 更新服务器 启动测试的Mqtt服务器  
Edge:D1B8E27F6001 C  
服 设备监视界面 采集信息-Edge:D1B8E27F...×出当前选择设备路径：Edge:A8907FFOBD68 /工厂-/车间A/设备A01/原始数据/温度 设备数量：2|50系统的树节点信息:(右键操作，支持拖拽，复制粘贴) 是否显示数据类型名称 以字节为单位的解析 □数据预览HEX OASCIIDevices D0 short白工厂 0366801230300223238四车间A 温度设备A01产量原始数据303132333435363738394041424344454647484950515253545556575859田温度设备A02车间B工厂二Regular 606162636465666768697071727374757677787980818283848586878889Alarm且OEETemplateDatabase90919293949596979899 06 107 108 110111112113114115116117118119

# 3.5 结构体的配置

如果你有一些结构体数据（例如你的 PLC 数据里有 5 个提升机的数据，数据长得一模一样，除了偏移地址不一样，这种情况不需要重复配置，只需要配置结构体就好了）。那么我们需要先去建立结构体的基本解析规则，字节长度信息等。

结构体的配置分为两种情况，局部结构体和全局结构体，顾名思义，如果一个结构体定义之后，多个不同的设备都可以使用，则需要配置为全局结构体，如果仅仅是当前的设备需要使用，其他的设备不能使用的话，则需要定义局部结构体。下面先说明全局结构体。

![](images/0be158dd5beeee589756ed15f08209f4dfcaca8b63565424ee62fca60ea2f799.jpg)

配置好后，点击结构体A 的节点，会出现字节信息，可以添加相关的节点信息。

![](images/4ce75bcd6ac489fb819ff20fade0aadf6592a89c5cc455774146a0dad4a11f79.jpg)

这时候还是什么都没有，我们再添加结构体的子数据结构信息，这个操作和上小节（3.4）所演示的类似，我们来添加信息：

![](images/cff66b8a27b460fb778bc574e0d5b9bc67c88333e15bdebd845491cc503c109d.jpg)

![](images/505ab9ee7c8ea06683e7c8873bfb3310a953dd7e3a6ae60b4d988022e15f17c6.jpg)

![](images/1276a992940de82e4a68a1fa637a450cfeef73699855bf9588af2b6bee8e6e5d.jpg)

这样就创建好了，我们回到原始数据里添加结构体解析，就出现了可供选择的结构体。这部分的解析规则和之前原始字节请求及解析部分类似。

![](images/bbe15f19532bca5f9e16fc73c003c7f32dda206d7ff0e9884a9516431c61d233.jpg)

点击确认成功添加结构体之后，就出现如图所示的内容。

![](images/e910f095ef6f9f42cfb9c35801746fc567f709f507e2777b387281a5bc818298.jpg)

我们看到提升机的内容已经出现在右侧的数据里了。我们看到提升个数这个数据属于提升机1的，如果想要属于设备A01 本身，那么就上述界面的选项结构体是否展开就需要打勾。

我们看到上述界面的右上角还有个数据预览的选择，在边缘网关内置的实现的大部分的设备都支持了数据预览操作，我们可以打勾看一下：

![](images/39d22edca3df14d8edee1e5d648475ee9b172946e0b591577f6b7434b9c5b7b3.jpg)

如果是配置了 bool 的数组呢？我们新增一个，并且监视测试看看：

![](images/4d689b4cabfa53304b0effd373e493be6cb8f25e69d25a5f87d5da63cbc4bb5d.jpg)

因为 IO 数据密集，所以无法根据一个 bool 一个小框框把所有的字节都转为 bool 查看，本身意义也不大，所以此处仅仅展示选中的 bool 变量信息。

如果是局部结构体的情况，基本的操作逻辑都是一样的，只是在创建结构体资源的时候，从设备上进行添加即可，在解析关联结构体时，本地的结构体资源也会在列表里显示出来。

![](images/8612757c7bffdba7bdf08c8837063fc2b3f00f3913f23f2e9ffd9f4c72c3ce14.jpg)

本地结构体资源适用于插件的定义，基于模板的插件里想要使用结构体技术的话，必须使用本地结构体资源，一起导出 XML 文件即可生成模板插件。

# 3.6 报警信息配置

在本边缘网关系统中，可以定义多种不同类型的报警，然后对某些数据点进行绑定报警操作，报警资源里可以配置报警存储到数据库。

无论如何，我们先定义报警，在一般的设备报警里，主要分为如下的三种情况：

# 情况一：bool 报警(通常 true 表示报警，false 表示正常)

假如我们的三菱 PLC 规定了 M0-M4（针对BOOL 点位）共有5 个报警信息，现在可以在网关里配置采集并分析出来报警的基本信息，我们先配置基本的报警信息，在报警节点右键添加。

![](images/1af5dc4b9674fb491d32ccd13eb7a089db1ab825da804a18ce613d8dd5017434.jpg)

![](images/03bbbbebf14431ce733a3fde815c220dbd80a1ba6e5bbb6e2245522f64d81c46.jpg)

这时候还有一种情况也很常见，我一开始的报警设置了提示等级，但是我希望 30 秒后自动升为警告等级，60 秒（从报警开始时间算起）后升级为错误等级，应该怎么设置。

![](images/ff9f5c8590734992597ebcf5956c4563acbe463857cb6dd0fc35a539802d7785.jpg)

最后点击确认，添加报警到报警资源里面，添加之后如下所示：

且 Regular白提升机结构体提升个数  
且 AlarmAlarm001设备温度过高报警1 设备无料报警

# 情况二：int 报警

事实上，还有一种报警的情况非常的常见，在 PLC 里配置了一个字数据表示报警，比如值0 表示没有报警，值1 表示报警 1，值2 表示报警 2。那么在网关里可以这么配置。

![](images/6b4b8924e7128c23e04b9f502809b35d5fba00adb47d58353e4e9ae4f24c7c65.jpg)

![](images/8343b1952ae4d64f7006e822dc8f296a1d5ed905015b3994c0ad31926c61cbe4.jpg)

报警动态提升的功能和bool 报警界面的一致的，此处不再赘述。添加好后如下图所示：

![](images/713fad04bfb41be26de7b645855ab39a5a5fd974b66290211ec04dfb85b3cce8.jpg)

# 情况三： 值范围报警

我们来看看一种特殊的报警，比如说一个温度数据，我们想控制范围，高于 100 报警，低于 80 报警，那么我们就添加一个数值范围报警。

![](images/b049e8ddd6ea9a38df0f8b6678b556a1e33d200ca382fef10319bd55553b7272.jpg)

![](images/8192d3e2172aa7e28db3139ced3901cdda54f2442b2b1b7070c6f2441f360612.jpg)

所有的报警信息都添加好之后，需要注意的是有两个范围等级可以设置，添加好后界面如下所示：

![](images/e95f6e9803d9db4c8a01da8a0eaae5b3a13819a8f1230af88a7a8ee79a068e42.jpg)

显示了我们刚才添加的报警信息。在配置好报警信息之后，就开始对采集的数据进行报警绑定操作了。

# 关联报警信息

已经上面的操作，现在已经配置好了基础的报警资源，接下来就需要做相对应的关联数据。比如配置采集 M0-M4 的bool 值，然后绑定报警信息。还是以之前添加的三菱 PLC（设备A01 为例子）

![](images/b76d339f8fea7d8891d4a7b5a8ac25f05fbbc26b22c42e1493c9a5edcb4342c9.jpg)

![](images/57cd4e3bafcaf8405c21a0e6e870fceb2d48e29c64b6bfb916437ba9eecca098.jpg)

此处举例是针对Bool 类型报警节点配置，只能在报警分析里绑定bool 类型的报警，否则无法进行绑定操作，其他类型的报警关联都是类似的，例如新建 int 型的变量，然后绑定 int 型的报警。

# 报警信息存储到数据库

在网关系统V1.6.0 版本里实现了报警信息自动存储到数据库的操作，再进行存储报警到数据库之前，需要事先定义好数据库的资源，此部分内容可以参考3.8 小节里对数据库的定义操作。

当报警刚开始或是结束的时候，都将触发存储事件，我们只需要配置一些条件筛选，配置写入数据库的 SQL 语句即可，如下所示：

![](images/d718f4fee9d11368c4f28baddc647255dba59e481861359c62347beb1333f08e.jpg)

# 比如数据库中的报警表设计如下所示：

![](images/1426329bb0fddc393c1aed7a3af61ddd9cad0c3ec1cdb1242037a1cbdb277b78.jpg)

那需要配置如下的界面信息，需要注意的是，此处的报警等级支持配置是否按照 1,2,3,4 数字的形式来存储，还是按照字符串的形式来存储。

报警存储数据库操作 □ ×  
报警属性列表 节点信息数据名称 数据类型 描述 节点名称： 报警写入数据库节点  
Uniqueld Int64 报警的唯一ID信息 请求描述： 一次的数据库请求调用  
AlarmCode Int32 报警的代号信息 触发时间： ○报警刚发生时 报警刚结束时  
AlarmContent String 报警的详细内容 报警等级格式：O使用Int存储：1,2.3.4 O使用字符串存储，Hint,Warn,Error,Fatal  
StartTime DateTime 报警开始时间  
FinishTime DateTime 报警结束时间，报警结束状态有效 数据库信息  
Degree Int32 报警等级， 1:Hint,2:Warn,3:Error,4:Fatal 选择的数据库信息： SqIServer  
Status String 报警状态，Alarm,Finish,分别为报警中，结束 执行的SQL语句  
DeviceName String 报警关联的设备名称信息 INSERT INTO [dbo].[Alarm] ([DeviceName],[Code],  
TagName String 报警关联的数据标签名 [StartTime],[FinishTime],[AlarmContent],[Degree]) VALUES({DeviceName},{AlarmCode},{StartTime},{FinishTime},{AlarmContent},(Degree])确认 取消

# 保存之后，重启网关，然后看下运行效果：

文件（F)编辑（E）视图（V）查询（Q） 项目（P)调试(D）工具(T）窗口(W）帮助（H)  
对象资源管理器 v 4 XSQLQuery1.sql -12..1.Test (hsl(57) x DATHLIN-COMPUT.st -dbo.Alarm 属性 4x  
连接碧丫 /\*\*\*\*\* Script for SelectTopNRows command from SSMS \*\*\*\*/ 当前连接参数  
127.00.1 (SQLServer 12.0.5000.0- hsl) SELECT IOPV100 LD] 图日□数据库 [Code]库快 田RepotseveTePDB FROmest].dbo][Aam]□ Test数据库关系图日表系统表田FileTables 连接名称 127.0.0.1 (hsl)dboam 1数据保存的区域ID (PK, bigint, not null)目DeviceName (nvarchar(30), nul)Code (int, nul)Code StartTine FinishTime AlarmContentDegree23 田引 5 西门子设备 0 2023-02-252109:02.0002023-02-2521:10:06.00报警1统计信息田国dbo.Table1dbo.TableA01田视图田同义词

# 3.7 OEE 信息配置

假设我们的设备A 有个状态的数据信息，表示设备运行中，休息中，计划停机，维修中，这样就可以在网关里进行自动的分析各自的时间统计，分析有效工作时间占比。

那么我们先配置OEE 的基本信息。在总的 OEE 节点添加一个子OEE 节点。

![](images/26d9f60c38a8d3c2a091a0c19dfd973f78d8513cf6f13c0ebb9ffc7a3c118c2b.jpg)

# 然后就是按照要求输入相关的数据信息：

![](images/4de56a1ff77c955acf7f069483c6dd816f17914d7b3b25e1d2074e2187267a8e.jpg)

需要注意的是，运行中的状态，是否属于有效工作选择框需要至少打勾一个，这里可以有多个的有效工作的状态。然后点击确定按钮，这时候就配置好了 OEE 节点数据信息。是否属于有效工作是计算OEE 的工作效率的本质，系统将统计该状态的耗时。下图右侧是一个班组的设置的例子。

![](images/9ede95a3b6ef42a058c36775e59587bd7d80c09e8b2202adbea2f85f37093969.jpg)

然后我们在添加一个采集的数据，然后绑定到配置的oee 信息。

![](images/a8f658a6a740c60c96070894a017ddb8903f8d76ad5dcc134f05c0c85ccf45bb.jpg)

![](images/86c5ca6633aa854ca1b0c1e7b83ebff3e66496a75001ae764289b473f12ee4ba.jpg)

# 3.8 数据库请求配置

如果我们需要将一个设备的数据存储到数据库，例如 SQL Server 数据库，那么可以使用本小节的操作来实现，如果想要知道一个设备有哪些数据内容可供存储数据库，那么就需要将配置下载到网关，然后重启网关操作（参考后面小节的内容）。再增加数据库之前，需要先配置数据库信息：

![](images/ed3be4f96b30e992d699bdc403e02021137aa68e2f50026e1dec8a41a66339d5.jpg)

# 这样我们就添加好了一个数据库的对象，如下图所示

![](images/e4132986295e92da1e718b27224a3b081583c4f2a0d5a060dc035243abbffb97.jpg)

成功添加了数据库对象之后，我们继续回到设备的添加请求的功能上，点击数据库调用请求。

![](images/8d88657b2e4ad1ac1667c059361c541930fe4cb3237c34c73d2ce76c3c9c6265.jpg)

接下来我们要根据实际的数据表来修改 SQL 语句，例如我们有个数据表

![](images/e02016cb4bffef0c3fd0bcdc5167bef77d31a716cc2d9d63ca006aeb4e50787f.jpg)

那我们的 SQL 语句如下所示。

# INSERTINTO[dbo].[TableA01]([Col1],[Col2])VALUES({产量},{温度})

![](images/a98b0236075ff028f42ba64f9e438c7b7773bdaf3fb904088a1eac2a89079c81.jpg)

# 我们登陆到数据库查看，查看数据表

![](images/c445bf74d498b8bf6dadfcb0aedb1723cb5477d74baa52abc7286415010ec63e.jpg)

这里还少个时间，通常我们都会在数据库定义时间信息。

<html><body><table><tr><td></td><td></td><td>列名</td><td>数据类型</td><td>允许Null值</td></tr><tr><td rowspan="5"></td><td>D</td><td></td><td>bigint</td><td></td></tr><tr><td>*******</td><td>CreateTime</td><td>datetime</td><td>□</td></tr><tr><td>Col1</td><td></td><td>int</td><td>□</td></tr><tr><td>Col2</td><td></td><td>int</td><td>□</td></tr><tr><td></td><td></td><td></td><td>□</td></tr></table></body></html>

# SQL 的语句变为如下的内容：

INSERTINTO[dbo].[TableA01]([CreateTime],[Col1],[Col2])VALUES(GETDATE(),{产量},{温度})

这里使用的是数据库本身的时间信息，我们把参数配置写入到设备，再重启操作。

我们再去查看数据库的内容时，发现已经可以存储数据信息了。

![](images/f2dd17359d766a9f2ba19938dc53cbfdb16a1a48a9d7c66c5a4bf4acf8362f73.jpg)

需要特别说明的是，目前暂时只支持标量的数据进行存储操作，不支持数组的形式。

# 3.9 下载配置到网关

经过上面几个小节的操作，添加了一些设备信息，数据请求信息，报警信息，OEE 信息，其中报警和OEE 不是必须的，按照实际情况来配置即可，现在我们把配置好的内容下载到网关，然后运行刚才配置的设备数据，点击一个快捷键（下文提到的下载设备都是这么操作）：

![](images/96d6d54f2623a9f2eaaa8ad1cef50920b06ea48d3876df652fbcad9c466e5412.jpg)

# 然后就提示了下载成功

![](images/cbec82c040c15bceda8cde46103189848830f6d63ba8a172599bcc0ccbe8a0c4.jpg)

我们把服务器程序关闭重启，（如果你使用的windows 平台的程序，也可以从客户端重启，未来实现 linux 平台的远程操作重启）。

E\HsITechnologyEdge\HslTechnology.EdgeServer.exe 1 □ ×1 N 配 >Thread 件路径： E:\Hs1TechnologyEdge\devices.xmlThread 行的信： sch6.92 行的CLR 的 计算机信息： 3OK19P-209E55Trea 件成 君动 F 2 车间设备011700100Tead t tSer er[001] Web rV 加载成功！ wait for connectionsTread 180 EdgeSe S+ ces 远程间0耗时：42msThread [812] 程 启动Thread 1secMc3E 出 /车间 A01 [127.0.0.1 1:6000] 00 居成功[4/0] 40.916 Thread c3E] 车间 A01 [127.0.0.1:000 集数据成功 4/0 2工轮

如果是远程重启，可以在网关右键操作，点击之后网关就重新成功了：

![](images/bc95919e719001589b232e2866ec88cb783fc337d68d0e372a17b079554cf2ca.jpg)

# 3.10 查看网关数据

经过上小节的重新启动边缘网关服务器之后，我们就可以查看网关系统的相关基础数据，我们在客户端的界面只需要找到对应的网关右键操作即可。

![](images/5b8dad55c55b9d6651b3b7d44c2dd8db1938cefa4973356ffaa26544476cd6e3.jpg)

# 基本信息

![](images/edb36dbbe5f42309eb0d5b144e3a97e4f2059fd4cdbd4a066b89c7197a6e1a88.jpg)  
历史运行情况

在网关信息界面里，显示了网关的基本运行数据，包括当前网关服务器的操作系统，框架版本信息。以及内存占用的实时曲线信息。（注：该内存是.NET 框架里的内存，和实际的物理内存有出入）

文件(F）视图(V）工具(T）关于（A) 更新服务器 启动测试的Mgtt服务器  
本地网关  
服务器列表 中 设备监视界面 本地网关×  
+ 服务器基本数据■本地网关[127.0.0.1] 数据名称 值(Value) 类型 权限 描述网关名称 Edge:431572DD8C19 String Read 网关的配置名称公司名称 杭州胡工物联科技有限公司 String Read 网关的公司信息版本号 1.1.1 String Read 网关的服务程序的版本操作系统 Microsoft Windows NT 6.2.9200.0 String Read 网关的操作系统信息运行时 4.0.30319.42000 String Read 网关的系统的运行时环境版本编译版本 LinuxARM String Read 网关服务器的编译时环境启动时间 2022-02-07 10:36:57 DateTime Read 网关启动的时间内存使用 32.14 Mb String Read 实时内存使用信息活动时间 2022-02-07 10:39:09 DateTime Read 网关服务的主线程的基本信息超时检测数 Int32 Read 当前等待检测超时操作的数量内存使用情况 运行历史记录 上次未捕获异常MANUALMANUALMANUAL消息： MANUAL结束：2022-01-25 23:02:00 消息：Unknown，可能来自直接终止程序或是断电重启！ ABORTMANUAL结束：2022-02-06 23:01:36 运行：00:00:56 消息：来自远程客户端[admin]使用了重启命令操作！ MANUAL消息：Unknown，可能来自直接终止程序或是断电重启！ ABORTMANUAL开始：2022-02-07 10:36：57 结束： 运行：00:02:12 消息： RUNNING  
日志监控  
设备： 消息： 2022-02-07 10:39:10

历史运行情况详细的记录了最新 10 次的启停记录，以及每次启停时具体的操作原因，方便的进行排查各种问题。

# 3.11 查看设备数据

我们看到已经成功找到网关设备，显示了网关的信息，然后我们在客户端上查看网关里面设备的基本信息情况及数据。例如我们需要查看有几个设备，设备连接是否正常，每个设备有什么数据内容，以及当前的值，是否发生了报警等等信息。那么可以双击网关操作：

![](images/3094a6d5e671b64ec1b8a348729f7b590888b512cfddd6228afeccfd52588d07.jpg)

双击网关之后，出现下面的界面，左边显示设备的信息，数据变量名，右侧显示数据数据信息，及其相关的变量值。

![](images/1eb012e0db12a99cf5238cc2fad7841eed2703b83f0bd0c94bddc6e9299254b6.jpg)

在上图的第三部分，可以看到多个界面标签信息，当我们选择了不同的页面标签之后，就会查看不同的数据，比如查看了报警的信息，详细列举了当前的设备的报警数量，报警的内容，报警的等级，持续时间信息。

![](images/3d8fce36504b45c3d64d9d9ec436c06a6ab816fa514856967695396f3228653d.jpg)

# 如果我们想要查看 /工厂一/车间 A/ 的所有的报警列表，只需要点击右上角的报警标签即可

边缘网关系统客户端 □ ×  
文件(F)视图(V）工具(T）关于(A) 更新服务器 启动测试的Mqtt服务器  
本地网关 -。心  
服务器列表 中 设备监视界面× 本地网关  
+ 设备名称：Edge:B33520B93357 开始运行时间：2023-06-03 22:16:27 Version: 1.7.0 始2 01日。凶1  
□■本地网关[127.0.0.1] 设备2 报警2三 工厂 设备名称：设备A01 设备名称：设备A02车间A 活动时间：2023-06-03 22:21:24 活动时间：2023-06-03 22:16:27设0 集时：147ms 采集时：0ms 5.0分钟失败次数：1 失败次数：0品 温度 02 ×。 Xo 0。 ×。 ×o 0 点击报警信息

# 就会显示如下的信息:

![](images/a0dd4e3e1d173b3a2d33376f7df3a84c6f297f11a81ab2cec152936386c11697.jpg)

查看 OEE 信息，显示了设备当前，也就是今天的实时 OEE 状态，以及过去的过天的 OEE 统计信息，还显示了设备当前的状态信息。

![](images/2c0907cd5a01390f92a5c7a95c7c052f9cd29464afcbc24fa7c6ecd734b79beb.jpg)

# 3.12 暂停继续采集操作

在上小节查看了数据数据界面，可以发现设备一直在不停的采集数据，我们可以控制设备采集的启停，点击界面的设备的状态指示灯，看下面的示例：

![](images/1a7faf9c44c8717f6e00e0ffa826ed2a15b05b5d397e4979428990eff009045b.jpg)

这时候的状态指示灯会变成暂停形状的蓝色。

![](images/725b64d6e3bb57f46b8a16f01afcc531acd257009f315ac6c1cb15bc6dd84282.jpg)

然后发现提示暂停成功，状态的显示颜色发生了变化。如果设备本来是在线的，显示的时间就是暂停时间，如果设备原来是离线的，显示的时候还是离线时间。如果想要继续设备采集，继续点击下蓝色的状态信息。

![](images/d89bcf590ebd0664199f7448b1a6b6fbaaaf26a726828085a463e80aa162cb48.jpg)

我们可以对一个路径下的设备所有进行暂停和继续操作，在路径下右键菜单操作，例如下面，我们需要对车间A 下面的所有设备暂停，如下所示(继续的操作也是一样的)：

边缘网关系统客户端  
文件(F) 视图(V) 工具(T) 关于(A) 更新服务器 启动测试的Mqtt服务器  
本地测试 -=C  
服务器列表 中 设备监视界面 采集信息-本地测试  
+c 设备名称:Edge:A8907FF0BD68 开始运行时间：2025-本地测试[127.0.0.1]白工厂 1 车间A路径右键菜单 设备名称：设备A01 设备名车间 活动时间： 2025-02-09 14:00:38 活动时i1 设备暂停请求 点击设备暂停请求34ms 采集耗设备继续请求 成功次数：9967 失败次数：2 成功次温度 ? 0 ▲0 × 0 xo 00设备A02白u设备A03电压车间B工厂二Edge:A8907FF0BD68 [127.0.0.1]数据信息 设备信息方法接口报警信息OEE信息离线信息

我们也可以对整个网关所有设备进行暂停和继续，举例一种场景，在网关里配置了很多的设备信息，同时都在采集，那么日志数据就很多，没法单一的查看某个设备的日志，或是仅仅对某个设备进行调试操作。那么可以选择一键暂停所有设备的采集通信，然后继续一个设备的采集即可。

![](images/4f92794d6543acaa71df8cd527d77b0283d7f6a91b980c6d5a61eb28732bafd3.jpg)

暂停之后，这里的图标就会显示为一个播放状态，再次点击就是所有设备继续请求。

![](images/eb1189e73e213c9534d63008c668a9229216f95473173bf4a260378c9d51b13f.jpg)

# 3.13 设备数据写入操作

我们在数据信息界面，双击数据行，如果数据的权限是支持读写操作的，就可以直接对数据进行写入操作，我们看下面的示例：

![](images/fd59bc5aa5e68c8e99755d25dac936fc2cc0c4a7e58c6074bac05318ec95c628.jpg)

在点击写入后，我们看到提示如下：

![](images/cbc80b3696bc58b16fd396dbaa992b35a83963f6b0fc6f1b6cd1fad9f00efeb2.jpg)

这个值就被写入操作了，无论是管理员账户，或是其他账户，一般都是可以写入的，除非下面的 2 种情况：

1. 该账户的权限限制为只读（具体看章节《网关账户设置》的内容）  
2. 该数据节点配置了不允许写入操作，那么即使是管理员账户也无法写入操作。

我们来举个例子，针对边缘网关的设备 A01 的数据状态进行权限的修改，继续打开网关配置：

![](images/79154dc1601233b3e6df304dd562fbaacadcd7bbb2df2d811440bf5d7d1e3f01.jpg)

主要就是再数据权限处，打钩禁止远程账户写入的操作，如下图所示：

1 ×

![](images/adc4c7f250ecd1a536601f324f0486a5aac719eb9e3b3f5907e12092b2b2e031.jpg)

当我们把权限修改之后，然后参照3.8 小节的下载配置到网关操作，然后重启服务器后，再回到数据写入界面进行写入数据操作。

![](images/7a1b4ce8d53a762eee0aaf2213f784bf0bd04a7eed9eaaa1845e4a495dcd749c.jpg)

# 如何写入结构体的数据呢？就比如下图中的结构体提升机1 的提升个数数据

![](images/c421fffc9ab55a1d2cc7a228a8a13487f1a5976c0aa7f8ee6083afd8f9eb27c3.jpg)

然后会弹窗，我们输入了需要写入的实际数据之后，就可以点击写入按钮，然后提示写入成功。

![](images/4f21c9fcb095f082ac9dced54f70046d42d8a3d316446439a01b58fb7fdb5da5.jpg)

# 3.14 定时写入请求

我们之前的章节已经介绍了如何从网关定时的读取的功能，但是在很多的时候，我们需要实现对PLC 设备定时写入的需求，例如定时对某个地址写 0，来让 PLC 识别网关是否在线，或是对指定的地址写入特殊规则的变化。使用方法为，在指定的设备上，右键新增Request，然后选择新增定时写入请求。如下图所示：

设备监视界面 采集信息-本地测试  
品当前选择设备路径： Edge:A8907FF0BD68 工厂-/车间A/设备A01 设备数量：2|50  
系统的树节点信息：（右键操作，支持拖拽，复制粘贴） 数据列表Devices节点信息车间A 节点名称 设备A01设备A01 节点别名产 新增请求分类 节点的描述信息 此设备安装在角落，编号0001原+ 新增Request 中 新增标量数据读取设备A 新增本地结构体 新增标量缓存数据车间B 量 新增原始字节读取eg 日 编辑方法接口 新增定时写入请求 1 新增定时写入的请求Alarm OEE 啦 复制设备路径 导出XML ® 新增方法调用请求 新增数据库调用请求 V1.3版本以上支持 2024年08月18日Template 回 导出JSON 是否注册MRPC接口 FalseDatabase 编辑节点 是否注册WebApi接口 FalseX 删除节点0 向上移动 设备模板主题前置信息。 向下移动连续请求间歇时间 0L 节点属性 √ 网络层信息IP地址 *************  
网关备用及历史变更配置文件列表： 冗余IP地址

# 3.14.1 写入已经配置好的数据标签

下面演示把数据写入当前已经配置好得数据标签，因为配置好的数据标签，已经确定了数据类型，所以写入的字符串数据需要能转换为目标类型即可，比如此处的例子是上图中 Int16 类型的产量：那么我们写入的字符串只能为十进制的整型，表示范围是-32768-32767，超过范围会发生写入失败，错误例子比如输入 123456789 ，abcd，0x1000 （如果数据节点是 float 类型，那么输入的字串可以为浮点数，例如 1.23） 。

例如如果需要一直写入 0 数值。每秒写入一次，参考如下的配置：

![](images/78297503d43407e9dd9d277b94fd650f1d374c3ef24772890b24e7d1175bf626.jpg)

配置好后，写入配置文件到网关，然后重启网关。一直写 0 看不出来实际的效果，我们试试支持的第二种格式，循环写入不同的值，例如写入[1,10,100,105,20,5]，按照这个数组循环写入里面的值，那么直接输入 [1,10,100,105,20,5]即可。

![](images/529fa6bcda911908a320771cd26976a38c31be759a614f272936215fa761ca01.jpg)

如果我想从1 写到 10000 呢？岂不是要写10000 个长度的数组？答案是根本不需要，网关支持直接使用范围的表示方式，例如从1 写到10000 表示为：[1-10000]  ，需要注意的是，累加为1 是固定的，当然也可以输入[10000-1]，这时候数据是递减的。这种方式只适合整数的范围写入，不支持浮点数，数据的表示不能带有空格。

# 3.14.2 写入一个脚本的生成的数据

如果我们想写入更加复杂一点的数据，支持使用脚本的方式，可以利用C#的 System.Math 命名空间下 函数，例如 Sin,Cos,Abs,PI, 等等。例如想要创建一个周期为 100 秒，变化范围 50-150 的正弦曲线，那么只需要输入 eval=(short)(50\*Math.Sin(2\* Math.PI\*x/100) + 100) 其中的 x 是请求次数的增量数据，从1 开始增加。Math 的其他函数也是可以使用的，例如指数，对数都是可以使用的。

![](images/11952518bbffc52a842cc83eb26bfba648337166f55f32a529b17f90e1fe7ff7.jpg)

我们可以看到数据按照我们的设想方式写入了。如果我们想要写入一个随机数，可以编写下面的脚本信息：eval=(short)new Random().Next(100, 200) 就写入了一个 100-200（不含最大值）的随机数，写入数据之后，重新启动我们看下曲线。

![](images/5a9141230c717a0b97f729602a7f1dad7f37646321b54a2719de4d14dca06d6d.jpg)

# 3.14.3 脚本里关联本设备下的其他数据

如果我们脚本里想要关联本设备的点位，进行一个数据加工处理，可以这么写脚本代码，例如我们这个设备A01 的产量=产量+20，那么脚本应该这么写：eval=(short)(温度 + 20)这里的能输入的标签名称就是设备下的存在的数据名称，下图左侧有显示数据列表，脚本如下图所示：

![](images/8ad7e0345f9d18b59a9a299789b9c1527e14067ec9742593f34f50dd7c33b599.jpg)

# 运行之后，我们看到如下面的值变化：

![](images/553bb7e5cc6c7a8a0155d3ee02155a27e126799815622b4ba791097dd23d05c9.jpg)

# 3.14.4 脚本里关联网关下其他设备的数据

如果我们的脚本里想要关键其他设备的值呢？也是支持的，我们再创建一个设备 A03，然后创建一个请求值，如下图所示：

![](images/4dc842652ca7f1e2481646129c1c94e6b391dc3747a51c716e25acbb15d26f8f.jpg)

然后我们回到产量的值信息，让其等于设备A03 的电压\*2+10，然后脚本这么写，然后重启：

# eval=(short)({工厂一/车间 A/设备 A03/电压} \* 2 + 10)

这里的其他设备的变量使用花括号来表示，配置的截图如下：

![](images/c3d99470d48ea3bd91a60ed969699db6df09e8e1164ad74cced059e2cdf88ecd.jpg)

# 3.14.5 直接关联网关下其他设备的数据

我们再来看看最后一种的写入方式，从网关另一个标签写入值，通常在两个设备有数据同步需求的情况下，会有这个功能的需求。

例如输入字符串 var=工厂一/车间 A/设备A01/温度   ，表示获取网关的这个名称的数据，然后再写入到产量里去。温度是什么数据写入到产量是什么数据，需要注意的是，两个变量的类型应该是一致的，或是可以相互转化的，这样的值才能被接收及写入操作。

# 3.14.6 把数据写入绝对地址的

上述小节是写入已经配置的数据节点的情况，有时候我们希望直接使用设备本身的地址写入设备，或是我们创建了一个虚拟 Modbus 服务器，然后直接写入变化的测试数据，那么这时候直接写入地址来的更加的便捷。因为通常情况下光有地址还不知道数据的类型，所以当指定写入绝对地址时，必须指定类型。

![](images/3a7af325a391651fb8bad6ef16cc63909f56e26de280914f2e9cf2f23272c012.jpg)

写入的地址需要PLC 本身或是虚拟服务器本身支持的，否则也无法写入。写入数据的五种格式方式和上小节的介绍是一致的，这里就不再重复赘述了。

# 3.15 定时方法请求

如果当前的设备支持方法数据接口，则可以新增方法的接口调用请求，例如 FANUC 机器人的数据采集信息，我们来说明下如何调用的。我们回到边缘网关系统的客户端的节点配置器界面：

![](images/403af51e2b5f9f1a1317ac10e289e34f0b77df7bf35897c46e6ce130766debda.jpg)

点击确认添加之后，就在节点上生成了机器人的节点信息，然后右键添加相关的方法请求，如下：

![](images/d0e80db6f8f2f7db23e28068b5632d10758434be1db1dac1fcc0567d6bdf1c7f.jpg)

![](images/2eca935f49341b3d0bace919911abe72c078debf0a973c778d8cec6c0941aa7a.jpg)

# 我们就成功添加了一条方法的请求信息。

![](images/ab7378b4f325ca43bc4795300dcf77e821c896c11bbd2348627b0a29ec56d77b.jpg)

然后配置信息下载到边缘网关服务器，并重启边缘网关服务器软件，在客户端重新刷新查看：

![](images/d72c5a85213e75acde237ab159ffa162bd571985f3dab417309fb927e8c6bece.jpg)

我们点击设备的方法接口界面，可以看到当前的设备支持的所有的方法接口，每个设备支持的接口不仅相同，需要在方法接口界面进行确认。每个方法都有名称，传入的参数信息，以及返回的数据信息，都在签名列里进行显示，如果返回的类型带有 Task 字样的，说明该接口方法在边缘网关服务器使用异步调用实现。此处当点击 Fanuc 机器人的设备的方法接口时，显示如下的内容：

图边缘网关系统客户端 □ ×文件(F）视图(V）关于（A)更新服务器 启动测试的Mqtt服务器  
本地网关 -e○  
服务器列表 口 设备监视界面+ 设备名称：Edge:C2F6CCABEA55 开始运行时间：2022-01-0418:50:31 Version: 1.1.0 63 020。81□本地网关[127.0.0.1] 设备3 报警工厂 设备名称：设备A01 设备名称：设备A02 设备名称：Fanuc机器人设备车间A 活动时间：2022-01-04 18:52:19 活动时间：2022-01-04 18:52:19 活动时间：2022-01-04 18:52:19设备A01 采集耗时：163 ms 采集耗：0ms 采集耗时：5ms设备A02 成功次数：428 失败次数：0 成功次数：0 失败次数：0 成功次数：107 失败次数：0白Fanuc机器人设备 ×。 xo 0。 A0 ×。 xo 0。 ×。 xo车间B0 选择方法接口菜单数据信息设备信息方法接口 报警信息OEE信息离线信息设备url: 工厂-/车间A/Fanuc机器人设备方法名称 签名 2双击需要调用的方法 描述ReadFanucData Task<OperateResult<FanucData>> ReadFanucData() 读取机器人的详细信息，返回解析后的数...ReadSDO Task<OperateResult:Booleanl]>> ReadSDO(UInt16 address,UInt...读取机器人的SDO信息Read the SDO inf..WriteSDO Task<OperateResult> WriteSDO(Ulnt16 address,Boolean value) 写入机器人的SDO信息Write the SDO in..ReadSDI Task<OperateResult<Boolean]>> ReadSDl(UInt16 address,Ulnt16... 读取机器人的SDI信息Read the SDI infeor..WriteSDI Task<OperateResult> WriteSDI(UInt16 address,Boolean value) 写入机器人的SDI信息Write the SDl infor..ReadRDI Task<OperateResult:Boolean|>> ReadRDI(UInt16 address,UInt16.. 读机器人的RDl信息WriteRDI Task<OperateResult> WriteRDl(UInt16 address,Booleanll value) 写机器人的RDI信息ReadUl Task<OperateResult<Boolean[]|> > ReadUl(UlInt16 address,UInt16 I.\* 读机器人的Ul信息ReadUo Task<OperateResult<Boolean[]>> ReadUO(UInt16 address,Ulnt16... 读机器人的UO信息WriteUo Task<OperateResult> WriteUO(Ulnt16 address,Booleanl value) 写机器人的UO信息ReadSI Task<OperateResult<Boolean]>> ReadSl(Ulnt16 address,Ulnt16 l.. 读取机器人的SI信息  
日志监控  
●设备：本地网关 消息：[18:50:38] Edge[本地网关]：读取服务器数据成功！ 2022-01-04 18:52:19

双击方法的接口的行，就可以对方法进行调用测试，在方法接口测试事，需要输入参数信息，单后点击调用，在结果信息里查看当前的返回信息。

![](images/2898fe60bf998dd8ff309691487e3fe7507ece2cc390159e036976050385e592.jpg)

其他的设备的方法接口的调用都是类似的。

# 3.16 请求分类及多个请求同步

当我们需要对多个请求的数据进行一起做些控制的时候，例如网关的配置文件里配置了 10 个数据请求，现在要统一的设置生效或是不生效，这时候我们就需要在设备下面添加一个分类信息。如下操作：

![](images/a7451694794b1aa0825bb87cbb54ec6fb4e7e59910e8d43b815e8765add40aa3.jpg)

# 然后我们在该分类下创建几个请求，最终创建完成后的截图如下：

![](images/e0f30b3b91216bad0762c9c705d96423b2073fc600a80868f6ea94ae42abb8bf.jpg)

如果没有额外设置过分类的属性的话，那么此时和没有分类是一样的效果，但是如果此时我们希望让分类一下面所有的请求都失效，此时可以设置分类的属性请求是否生效设置为 False：

![](images/99e00cd34bfac749dbc421d6e4254de4a6b56aff4fdd952059da7e6e418bdc6d.jpg)

设置之后（下载重启生效）在设备对象的实际循环周期里，就不会对这些请求进行读取操作。

但是这时候还有一个小问题，如果我希望这个十个请求始终是在线程的一个循环扫描周期执行的，虽然上述这么配置（十个数据请求都是 1 秒周期）一开始是在一个扫描周期一起执行的，但是随着时间推移，难免部分的请求因为网络波动造成请求时间比较久，久而久之，可能会造成一秒周期内有几个请求是100 毫秒的时刻请求，有个请求是 500 毫秒的时刻请求，有几个800 毫秒的时刻请求，可能也会造成数据的更新不同步，可能不满足实际的要求。我们的实际需求是这十个请求需要在同一扫描周期内执行，无论过了多久时间，这时候我们就可以对分类节点的属性请求同步信息进行修改，设置为 True，然后下载到网关，重启服务器即可。

![](images/1fa67d3fcd2e2a7bcf1abc8a824f209fb3e03c43ce6067307f4b75c6e4db5e5b.jpg)

# 3.17 请求的附加条件

有时间我们的请求（数据读取请求，写入请求，方法请求，数据库请求等）并不是定期执行的，而是在一定条件下才执行的，例如：设备状态等于 1 的时候，才读取产量数据。我们先创建两个请求，一个设备状态，一个产量请求，效果如下：

![](images/5415c6cb2c85d13712ce485b9e9a7dfcf25c08488b0f769e3268c648c714a4f1.jpg)

# 我们继续编辑产量的请求标签，设置好条件，如下所示：

![](images/d0a29f138de241266730ee2dcb8a79834eeaec7ebfc804e02e58e1ac15f56b69.jpg)

我们看到需要先输入附加条件，这是一个脚本语句，可以使用设备当前的内部变量值信息，脚本需要返回 bool 值，以上脚本是判断是否等于指定值，当然也可以使用多个变量来做更加复杂的判断，例如：(A\*10+B)>30，这里需要注意数据的类型，如果变量A 是字符串，那么脚本就会直接执行失败，返回异常的消息。

继续看触发方式有三个选择，分别是上升沿有效，下降沿有效，满足时持续有效，这里是针对当前满足的附加条件而言的，上次不满足条件，本次满足提交，就是上升沿；上次满足条件，这次不满足条件，就是下降沿；本次满足条件，就是持续触发。通过脚本的实现，可以满足一些实际情况的场景。

# 3.18 网关账户设置

上述演示时，都使用的管理员的账户信息，管理员的账户权限是最高级的，可以请求边缘网关服务器软件的基本配置信息，设备采集配置信息，点位表信息。

但是在实际的使用过程中，可能会有不一样的需求，比如边缘网关系统的设备数据需要对客户开放，但是不希望开放 PLC 的代码，或是不希望开发所有的地址表，那么我们可以配置普通账户，一个网关可以配置3 个普通的账户信息，当客户使用了普通账户登录时，则无法使用配置功能，只能浏览边缘网关系统的最终数据信息。

![](images/b01afa11f19f755c3968287f8459badf377946d467910c985b50ff44ab0fde51.jpg)

比如我添加一个 test 的账户，然后保存。重启服务器。然后我们再使用 test 账户添加一个网关，然后去点击看看。

![](images/6352298b3b1669b15e473feccf97daa21417446fc55d3fd914e2e4601bee617c.jpg)

当我们再想去配置节点信息时，就会弹窗权限不足，但是可以查看数据内容，或是读写操作：

![](images/1bf2a2683d71a8a7794d967a27d542bd64b9b48422abf31890849df7bfee6550.jpg)

# 3.19 网关日志设置

边缘网关系统的服务器软件在连续运行的时候，会产生一些日志信息，在默认的情况下，这些数据是在边缘网关系统服务器软件上打印显示的，并没有存储文件，也没有按照等级过滤，在默认的情况下，如下所示：

![](images/1df267b32d710ebe4cd80c54d0843719e8b76270663faa57186c810f7cfe923b.jpg)

我们看到有不同等级的消息，分为：调试、信息、警告、错误、致命五个等级的消息，每种消息的显示的颜色也不一样，如果我需要对调试消息进行过滤，或是将日志存储文件，那么可以在服务器参数设置界面进行设置。

![](images/e2f5df7d096c262111467729f46cce57a515cfe745a98e7bb8264f31161a5eec.jpg)

保存之后，重启边缘网关系统的服务器软件。

E:\HslTechnologyEdge\HslTechnology.EdgeServer.exe □ ×言息 2021-08-18 20:15: 753 Thread[026] "elseclc3E] 127.0 ：600 据成功[4/0]_2021-08-18 20:15:07. 330 [021 anucInterface 间A/Fanuc机器人 [127.0.0.1:60008] 周用[Dat： ] ReadFanucData 数据失败：Socket Exception -> 由于目标计算机积极无法连接 127.0.0.1:60008[H] 车间AF: 机器人 设备] [127.0. 1:60008 采集数据失败[0/1] 持续：1.23小时2021- 1820:15:07.772 Thread 1secMc3 /车间A/设备 备A01 [127.0.0.1:600 采集数据成功[4/0]！ 耗时 ms2021-08 791 车间 备A01] 1270 6000 数据成功ucInterface 日A anuc机器 备 0A08 周用[Data] ReadFanucData 数据失败：Socket Exception ->由于目标计算机积极去连接-18 5 rfac [工厂 /车间A/Fanuc机器人设备]_[127.0.0.1:60008]采集数据失败[0/1] 持续：1.23.小时计算机积极拒绝，无法连接。127.0.0.1:1883息 2021 -18 20:15:09.811 [021] [1 工 车间A/ 设备A01] [127.0.0.1:6000] 成功[4/0]！ 耗时 2ms2021 08 -19 20:15:10.830 Thread [Melsecc3E] 行 /车间A/设备A01][127.0.0.1:6000] 采集数据成功[4/0]！ ms备 [1270 用D： ReadFanucData 数据失败：Socket Exception -> 由于目标计算机积极  
3 + 27 1:60008ucInterface 车间A/F uc机器人 集数据失败[0/1]持续：1.23小时  
信息 2021-08-18 849 hread [Me1secMc3E][工] /车间A/设备A01][127.0.0.1:6000]采集数据成功[4/0]！耗时：IQTT上传 标计算机积极拒绝，无法连接。127.0.0.1:1883  
信息 2021-08-18 20:15:12. 867 [017 [Me1secIc3E] [工厂 -/车间A/设备A01][127.0.0.1:6000]采集数据成功[4/0]！耗时：0msFanucInterface /车间A/Fanuc机器人设备][127.0.0.1:6008]调用[Data] ReadFanucData 数据失败：Socket Exception->由于目标计算机积极洋连接 127 1:6000车间AF: 集数据失败[0/1]持续：1.23小时2021 -1820:15:13. 887 ad [elsecllc3E] T 车间A/ 备A01] 耗时2021 19 20:15 887 Thread [Melseclc3E nucInterface 工 /车间A/设备A01] H 车间A/Fanuc机器人设备][127.0.0.1:60008]调用[Data] ReadFanucData 数据失败：Socket Exception ->由于目标计算机积极 耗时 ms27:15:15 418 sot ucInterface [1] 车间A/Fanuc机器人设备] 采集数据失败[0/1] 持续：1.23小时  
信息 2021-08-18_20:15:15.906 Thread [021] [Me1secllc3E][工厂 /车间A/设备A01][127.0.0.1:6000]采集数据成功[4/0]！耗时：Thread dgoS T上 127. 标计算机积极拒绝，无法连接。127.0.0.1:1883  
[信息]2021-08-18 20:15:16.924 Thread [021] [Me1secMc3E][工厂—/车间A/设备A01][127.0.0.1:6000]采集数据成功[4/0]！耗时：1mData数据失败： ception ->由于目标计算机积极

设置了之后，就不再显示调试等级的日志信息。

# 3.20 切换 DTU 模式

什么是 DTU 模式呢？英文称为 Data Transfer unit，叫数据转换单元，一般用来串口转网络数据的，在边缘网关系统里主要用于将本地的串口或是网口，转换为远程服务器上的网口。怎么理解呢？举个例子：

我们的设备一般是串口，或是网口的，一般是电脑的串口直连设备，或是使用局域网的 IP 地址来访问，在内网的情况下是没有问题的。如果我们需要在云服务器获取到车间现场的数据的话，我们在车间的上位机可以连外网之后，就可以把 PLC 的数据采集了，然后上传到云服务器。数据上传是没有什么问题的，只是如果需要从云服务器上写入 PLC 的数据呢？因为 PLC 在本地，没有 IP 地址信息，云服务器无法进行直连 PLC，这时候可以使用两种方式解决。

1. VPN 技术，将本地的端口映射到公网 IP 的一个端口上去。  
2. DTU 技术，同理，将本地的端口连接到云服务器上，在本地和云服务器搞出一个网络桥梁

简单说就是边缘网关系统的 DTU 功能可以让边缘网关部署到云服务器上去，配置 DTU 模式的三菱，西门子，modbus 等设备，然后在车间现场本地的电脑运行边缘网关系统，配置 DTU 转换设备即可。

# 云服务器端配置：

在云服务器端的配置需要启动 DTU 模式，所以需要先把服务器部署到云服务器。然后使用客户端连上远程服务器电脑：

![](images/f564f0f1cefbb9ffb511b42456f4d160da8992c9e99d90816525e8246ebafb37.jpg)

配置好后，我们开始配置 DTU 的设备信息，比如我们需要添加一个 ModbusRtu 的 DTU 设备，需要给这个设备唯一的一个识别码，因为没有设备的 IP 地址的概念了，所以此处只能使用唯一的 DTU 识别码来识别不同的设备。

![](images/c830d7e0b4cfdac9c44f2c74dcfb9a7268135260fe56144aa3c4f31fb44fd5bb.jpg)

然后开始输入相关的参数信息，然后添加一条采集的数据，此次演示随便弄个地址。

![](images/55102d604ae929293301ed54c85747a55c35fed660a92505937189314744a33c.jpg)

将配置信息写入边缘网关系统里，然后重启网关系统。然后监看设备数据：

![](images/c6955061f4d298d0bd5025842d5e9e2ed543038955898b660ccee23048e53814.jpg)

DTU 模式的核心思想不是主动去连接设备，而是等待该设备的连接请求上来，根据注册包的 ID 信息来将该连接对象，赋值给指定 ID 的设备使用，这个 Modbus 设备就可以利用这个连接对象来读写车间现场本地的 modbus 设备了。

那么我们在车间本地有个 modbus-rtu 的串口设备，我们再运行一个边缘网关系统的服务器在本地的计算机上，需要注意的是，该计算机需要可以连接到互联网的能力。

边缘网关系统客户端文件(F）视图(V）关于(A)更新服务器 启动测试的Mqtt服务器Foc  
服务器列表 4 设备监视界面 节点配置器×+ 函当前选择设备路径：Edge:21397258B61C I厂-/车间AEdge:21397258B61C [127.0.0.1] 系统的树节点信息： (右键进行操作) 数据列表工厂 Devices车间A 1运行在本地的网关 工厂 节点信息由设备A01 车间A 节点的描述信息 这是示例的分类信息设备A02 电设 + 新增分类Fanuc机器人设备 设 + 新增PLC等设备 节点名称 车间A申-OPC OpcDa Fa + 新增Robot由硫化机设备 -OPC OP 新增Cnc新设 ? 国 steIT 2 新增一个串口转DTU的功能Edge:DD5779EF9AF0 [192.168.0.110] Regular × 删除节点 固 TcpToDtu[网口转DTU]Edge:45131D6B5707 [192.168.0.104] Alarm + 向上移动白 Edge:61E0AACD9FD6 [*************] OEE + 向下移动由u ModbusRtu-OverTcp Database E 节点属性  
2

除了需要输入节点本身的名称信息之类，还需要输入串口的基本信息，串口的参数信息需要和设备致的，如果设备设置了9600 波特率，8 位数据位，1 位停止位，无奇偶校验，那么此处就需要配置一致，为了确认配置正确，最好在本地使用通信测试工具来测试下设备的串口参数。

还有一个DTU 部分的参数也很重要，和刚才配置云服务器时的参数配置需要一一对应，端口，密码，DTU 反馈确认都是按照服务器的配置来，关键是 DTU 标识，这个标识和配置的 modbus 的标识一致，这样才能正确的赋值。

![](images/7b86ba785db1af8820c98e0fdaf7c9857453e0237c713bc9001f93e28b27fb79.jpg)

我们配置好了，然后可以下载到设备，然后重启边缘网关系统服务器。这时候我们再去监视云服务器的边缘网关的 modbus 设备看看。

![](images/5ce6e7fac6dfdc823752a3e5fd14adb0a74535e952d08ebd68b2acc6c06e2c4e.jpg)

然后再去看看云服务器的采集情况，可以看到采集成功，其他的设备类型类似。

# □C:\Users\<USER>\Desktop\Edge\HslTechnology.EdgeServer.exe

p][192.168.0.100:502]采集数据成功[1/0]！耗时：78ms  
[信息]2021-09-05 21:44:02.609_Thread [012]_[ModbusRtu0verTcp] [ModbusRtu-0verTcp][192.168.0.100:502]采集数据成功[1/0]！耗时：78ms  
[信息]2021-09-05 21:44:03.624 Thread [012][ModbusRtu0verTcp][ModbusRtu-OverTcp][192.168.0.100:502]采集数据成功[1/0]！耗时：78 ms  
【信息]_2021-09-0521:44:04.624Thread[012][ModbusRtu0verTcp][ModbusRtu-0verTcp1[192.168.0.100:502]采集数据成功[1/0]！耗时：78ms  
[信息]2021-09-0521:44:05-624Thread[012][ModbusRtu0uerTcp][ModbusRtu-0verTcp][192.168.0.100:502]采集数据成功[1/0]！耗时：78 ms  
【信息]2021-09-0521:44:06.624_Thread[012][ModbusRtu0verTcp][ModbusRtu-0verTcp][192.168.0.100:502]采集数据成功[1/0]！耗时：78ms  
[信息]2021-09-05 21:44:07.609_Thread [012][ModbusRtu0verTcp][ModbusRtu-OverTc1[192.168.0.100:502]采集数据成功[1/0]！耗时：78ms  
[信息]2021-09-0521:44:08.609_Thread[012]_[ModbusRtu0verTcp][ModbusRtu-0verTc

# 3.21 主备模式

上述的功能都是针对的主服务器模式运行的，主服务器启动之后，就是加载设备配置文件，启动采集功能，如果在网关运行的过程中，硬件设备发生了不可预知，不可恢复的异常，导致边缘网关无法进行采集分析的时候，此时会发生数据灾难，部分的数据收集不到。如何避免这一个问题？可以使用两个边缘网关系统或是硬件，另一个边缘网关系统切换为备用服务器，启动时实时监视主服务器的运行状态，发现异常时，则自动切换为主服务器运行。配置备用服务器如下：

![](images/d45c908fa67a5aa25519e1012bc1b74120fbfee2f6d8838701e0d8b7895846ce.jpg)

在保存，重启网关之后，就本服务器就以备份服务器模式运行了，

2021-09-2612:58:50.405Thread 0012021-09-2612:58:50.406Thread [001]2021-09-2612:58:50.406 Thread [001]2021-09-2612:58:50.406 Thread 0012021-09-2612:58:50.406 Thread [001]2021 Thread 001  
息 202 Thread [001  
1 2021 Thread [001]  
息 2021-09-26 12:58:50.406 Thread [001]  
点意 202 02406 001 by杭州胡工物联科技有限公司  
息 2021-09-2612:58:50.406Thread 0012021-09-2612:58:50.407 Thread [001] 如需采购软件，请联系QQ：200962190Emai1:hs1200909163.com 微信：135167027322021-09-2612:58:50.407 Thread [001 012021-09-2612:58:50.411 Thread 001] ojects\Hs1Technology\Hs1Technology.EdgeServer_Net461\bin\Debug\devices.xml2021-09-2612:58:50.433 Thread [001]2021-09-2612:58:51.547 Thread 007  
息 2021-09-2612:58:51.558 Thread A 息成功！  
息 2021-09-26 12:58:51.582 Thread 011 成功2021-09-26 12:58:52.585 Thread

直到检测到主服务器异常了，就会启动和主服务器一模一样的数据采集分析操作。

# 3.22 更新服务器程序

如果想要更新升级边缘网关服务器的程序，可以直接使用新版本的程序去覆盖旧版本的文件，我们也可以从客户端去远程更新程序。

![](images/13d4200c0834d7d321defdff2b48c41fc2f8b5981caae8ce8eb5091149511321.jpg)

然后点击选择按钮，选择新版本的程序所在的目录，然后就可以启动更新操作。

![](images/c1e14a1984c83e1bec08f76f14a5cf096e813a728190c109b60675a59ba81e29.jpg)

启动更新之后，客户端会将目录的所有的文件传送到边缘网关服务器，然后通知网关服务器重启，网关服务器执行升级程序的脚本，完成升级。客户端界面最终提示程序更新完成，网关重启成功时表示成功。

![](images/8bff132d62c0e493f1ecf248fdd104bc544902ac2343e5007c76c20d307df34d.jpg)

当前的升级方式同样适用于 Linux 运行的边缘网关服务器，但是需要确保新版本的程序再目标环境是可以执行的，例如将 windows 版本的网关程序更新到 linux 系统里，则会出现无法运行的尴尬情况，需要手动重新拷贝整个的程序到目标环境才能解决。

# 3.23 网关备用配置文件

在配置网关的阶段，或是调试的时候，需要反复配置一些设备文件，然后频繁的切换测试的文件信息的时候，网关支持在服务器端备份相关的配置文件，当我们配置好一个文件的时候。

![](images/eaf2644b9bb062ce3cb182f51720d7bedf582854762c897e244ff2af57884eb3.jpg)

# 然后输入文件的名称：

![](images/2b5e3c6ccf0d458e50d2bb6e153572e988437ad2918d776c4c76539ed489dfba.jpg)

![](images/9d18754847a2ccb792fabd64fc9962caf3d1362799e7392bf27feadbbf0a121a.jpg)

XmlStandby 节点存储了备份的配置文件，可以随时的加载，下载到网关，下面还有一个XmlHistroy 节点存储了网关服务器最近的 10 次的变更 XML 文件，方便配置时，不小心覆盖之前的

配置文件导致找不回的bug，现在可以轻松的找回历史配置的文件信息。我们下载一次配置到网关看看。

![](images/f3ae6481da62321a6f3517763e366fd9f156703b6434fe834254d914108eb1fd.jpg)

# 3.24 节点复制粘贴

在配置网关的阶段，我们可以存在很多 PLC 设备，尤其是同一车间的或是同一产线的，除了 IP 地址不一样，其他的配置采集，数据点位可能都是一样的。那么我们就需要全部重新配置一遍，这样做非常的耗时耗力。本小节介绍直接的复制粘贴操作。

![](images/d7c3240fce2a42560390a45165420306ccfa2b9a459b160d4534601ba02494d2.jpg)

选中设备A01 之后，键盘按下 Ctrl+C 的组合键，然后确认选中设备A01 的情况下，键盘按下Ctrl+V 的组合键，可以看到下面的结果。

![](images/7624fb8ccd18df130beaa73ce777521a57822919d936e526c7fdb927e1cb2689.jpg)

展开设备A011 之后发现是一模一样的，现在只需要修改设备名称及 IP 地址。

![](images/b521b056cf61e79026f51b669f9e88252c3352361025422a026a9902e34dfb21.jpg)  
如果希望设备A01 排在设备 A02 之前，只需要拖拽设备 A01 到设备A02 上面去即可。

![](images/2b85a17969e4d51ef3628c00abfa4daf389fd05f974945ea4ee44e654032c58a.jpg)

当然同理。我复制设备 A01，然后选中车间B，再粘贴操作也是可以的。如下图所示：

![](images/af75edc8b885067d5c1ae343bb19437ce1492c3573ff57a707bdbd92b09acd7e.jpg)  
我们再来看看复制的本质是什么？我们复制设备A01 之后，粘贴到 txt 文件看看。

设备监视界面 采集信息-本地网关×  
当前选择设备路径：Edge:431572DD8C19 /工厂-/车间B/设备A01 设备数量：5|50  
系统的树节点信息:(右键进行操作，支持拖拽，复制粘贴) 数据列表  
Devices工厂 节点信息车间A 节点名称 设备A01设备A01 \*无标题-记事本设备A02Fanuc机器人设备 文件 编辑 查看DataDTSU6606-RTU <DeviceNode Name="设备A01" Description="此设备安装在角落，编号001"DeviceType="MelsecMcQna3E"CreateTime="2022-01-(车间BInstalatioDate="2--0414:6:0"Addres="118.246.22"Prt="6"CoctmOut="200"ReeivmeOut0设备A01 NetworkNumber="0" NetworkStationNumber="0" IsBinary="True">原始数据 <RequestNode Name="产量" Description="一次完整的数据请求" Requestlnterval="1000" Address="D0" RequestType="ScalarRead'报警 <RequestNode Name="原始数据" Description="一次完整的数据请求" Requestlnterval="1000"Addres="D0" RequestType="Sourcel状态 <RegularScalarNode Name="温度" Description="" Index="20" DataTypeCode="short" />数据库请求 <RegularStructNode Name="提升机1" Description="这是提升机1的数据信息"StructName="提升机结构体"StructIndex="50"Arrayl工厂二   
由Regular <RegularScalarNode Name="IO" Description="" Index="100" DataTypeCode="bool" Length="18" />  
Alarm </RequestNode>  
OEE <RequestNode Name="报警" Description="一次完整的数据请求" Requestlnterval="100"Address="M0" RequestType="ScalarReadLength="5" AlarmRelate="Alarm001" />  
网关备用及历史变更配置文件列表： <RequestNode Name="状态" Description="一次完整的数据请求" RequestInterval="100"Address=D10" RequestType="ScalarRea(  
XmlStandby ForbidRemoteWrite="true" OeeRelate="设备运行状态"/>演示测试版.xml <RequestNode Name="数据库请求"Description="一次的数据库请求调用"Requestlnterval="1000"Address="SqlServer" RequestTyp  
白 XmlHistory Enable="false"SqCommand="ISERTINTO[dbo].[TbleA01]([CreateTime],[Co1],[Col2])VLUES(GTDATE0,(产量}{温度)"/>devices_20220310155354.xml </DeviceNode>

由此可见，复制的本质是复制设备的 XML 基础文件，所以这里可以更加复杂的操作，可以先赋值设备 A01 的数据，粘贴到txt 文本，修改文本的内容，然后重新复制，粘贴回去。

![](images/30510a6b4ba86b379ec398da20db81c392ec569b5fd399bef5fea060e1acb1bf.jpg)

# 直接修改设备名称，ip地址

<DeviceNode Name="设备B1"Description="此设备安装在角落，编号001"DeviceType=MelsecMcQna3E"CreateTime="2022-01-0414:16:09"Instalatioe-:"tt"NetworkNumber="0" NetworkStationNumber="0" IsBinary="True" >

![](images/8321747ea8f9cdf5747a4ee46e96032cf07eb3dde54314c5ccbc116d2d287fba.jpg)

# 3.25 串行端口映射

在网关里配置增加串口设备的时候，通常需要选择串口信息，在Windows 系统里通常是 COM1 之类的格式，在Linux 系统里就表示为/dev/ttyAMA0 之类的，可能这些名称和你实际电脑（或是网关盒子）上标记的名称不一致，可能你标记的是 485-1，485-2 等，在配置的时候容易区分不清楚，也容易配置错误，所以从 V1.5.0 版本开始增加了串行端口映射功能。

![](images/018c043c7ab00184feedcc00d299568cd659c2e7ba940ae15592aa426e8f1f3b.jpg)

我们举个例子，将端口COM1 映射为 RS485-1，如图所示输入提示保存成功。然后我们在添加实际的串口设备时，就可以选择 RS485-1 了。如下图所示：

![](images/9373350597b078cc76a7d93737d242006ac3235cb1251e2bb49c016eeaeae4c7.jpg)

![](images/b34e110d7df48ee4786eb5baf98d4d4c0619cbce08043c1d5eacdd672bbbe1d2.jpg)

在网关服务器实际运行的时候，依然会显示实际的串口信息如果此处我们希望在客户端配置串口时不显示某个端口信息，例如COM3, COM4，我们只需要配置映射到空字符串即可。如下图所示：

![](images/ab9b93c6ece213dce73d63b8527a97811fbf09441af7f01e361dae44811b3a8d.jpg)

![](images/29e8605f65f06f24bcc7b11b9e7396ab44e397440f7a91531d06c9fa2aad3e84.jpg)

# 3.26 统一查看异常设备列表

在客户端主界面上，如果在边缘网关里，采集的设备数量比较多的话，我们可能需要快速查看所有异常运行的设备列表，那么可以这么操作。

图边缘网关系统客户端 □ ×  
文件(F)视图(V）工具(T）关于（A)更新服务器 启动测试的Mgtt服务器 1 鼠标放到这个图标上面，就可以点  
Edge:D1B8E27F6001 -=C 击操作  
服务器列表 4 设备监视界面区采集信息-27P3JG5RSP1DT.. 2  
+c 设备名称：Edge:D1B8E27F6001 开始运行时间：2023-11-1711:07:58 Version: 1.7.2 67 02①085  
Edge:D1B8E27F6001 [127.0.0.1] 设备2报警0工厂- 设备名称：水泵设备 设备名称：Dlt645OverTcpSIE水泵设备 活动时间：2023-11-17 11:07:58 活动时间：2023-11-17 11:07:585 Dlvercp 采集：0ms 失败次数：0 停用 采集时：4005 ms 失败次数：4486 2.51小时SIE西门子设备1 00 ×。 ×o Request [日期] failed: Socket Exception -> 由于.SIE西门子设备2-SIE西门子设备3SerialPipe由u ModbusRtucda-:Dn57E060n0CC [127 0011

点击后显示如下的界面信息，我们可以进一步的排序，过滤操作。

![](images/eedcae316ac2f5a782e6c1252d3083ebe8d3dbbc37514f90cf1fb548877c0c06.jpg)

当然，如果点击了在线设备数量，暂停设备数量，也是支持显示当前的设备列表的。

# 四、管道说明

什么是管道，在网关系统里，多个设备共同使用的通信对象称为管道，不同的设备可以共用一个管道，例如串口管道就是共用了一个串口资源，网口管道就是共用了一个网口资源。

# 4.1 串口网口管道

在实际的配置网关采集设备数据的过程中，会遇到类似以下的这种情况。有很多的 modbusrtu的仪表，使用485 总线串到一起了，然后转 232 接到电脑，或是转 TCP 接到电脑，现在要对这些设备进行采集。需要注意的是，串口网口管道都属于单线程模型，所有采集按照顺序触发。

那么现在有一种普通的配置方式，因为本质上只有一个串口，我们配置一个串口的 ModbusRtu设备，然后地址使用不同的站号来访问不同的设备的数据。如下：

![](images/3e57013b4cab535651dd016367d322f6d6b8503e391adecada0f35d532a8c827.jpg)

虽然这样也能读取到数据，但是读取到多个电表的数据挂在一个设备下，不是单独的设备，在网关上只显示一个设备的对象，并不能知道实际有多少的设备，大概如下所示：

![](images/b255f0b38e01be75cc69ce5424b8f4c713c7fbf83a12de988e84500ea880920f.jpg)

如果我需要的是每个电表都是一个独立的设备，那么可以使用共享的串口管道来解决。重新配置一下设备信息：

![](images/9b315ffdc52e06be6eb44321ae351c6fbea04559368275439f7ef3b32b71d966.jpg)

![](images/d77485ac69a6eb04f84f24067096d76cbadd0a3d83eabefb1abbb5c39f7dba0b.jpg)

我们可以明显的看到和之前的区别，现在是三个独立的设备，然后原始数据的采集和解析都是一模一样的（这部分都在第六章的模板里面进行进一步优化），三个独立的设备仍然使用 COM2 管道进行通讯，在这里需要注意的是，该管道下的所有的设备的数据交互仍然是线程互斥的，无法进行并发，所以采集的点数很多的话，或是有个设备掉线了，失效了，访问不到了，对整体的采集性能影响非常大。

现在将配置下载到边缘网关并且重启，在客户端看到的信息就不一样了。

![](images/2c763529a74939b329d8c06fe9290aeabe365e3b2adcd354b0c5597ca43f213d.jpg)

但是还是有两点可能不太满意，例如管道信息不想作为分类显示，希望这三个设备直接挂到网关节点的下面，以及数据不想以结构体的方式呈现，那么可以这么设置

![](images/9209f76fd3485281c8b3943402d0058d08e5112e00fcb4909ebb208fa20eff8f.jpg)

配置结构体解析的时候，三个设备都配置一下，我们再把配置写入到网关中，然后重启网关，在打开监视界面，我们就看到了更加简洁的界面：

![](images/646557e2164ea077b4ad14dbf91550f339d960a3d2e59610be34f403795aaca4.jpg)

如果将上述的例子改为串口转网口（透传的方式，不进行协议的转换）再接入电脑的话，则添加设备的逻辑仍然是一样的，无非就是两点不一样。

1. 原来添加串口管道，现在添加网口管道，配置 IP 地址及端口号信息，确认是否当作路径使用。2. 原来是串口管道下添加 ModbusRtu 设备，现在添加 ModbusRtuOverTcp 设备，还是站号不一样。

数据采集部分的操作及配置都是一模一样的，其他的不过多赘述了。

# 4.2 单线程管道

还有一种情况是一组设备里面，不管是串口的，还是网口的，都需要一个线程来顺序控制调度，而不是每个设备自己的线程控制。那么就可以使用单线程管道功能。

![](images/cc6e9d3afdd88b034cc23b6c2bb5a29c5802fff6d85ebd8f616b3ce3ccebd234.jpg)

![](images/123e55cbba4e7d8249666f7eaa1033e22a37781d208787ab5cac904278742428.jpg)

在单线程管理的设备模型下，只要有一个设备采集不上，就会影响管道里的所有的设备采集情况，如果需要各个设备互补影响的采集，那么就必须不能是单线程管道。三个设备的采集情况，会按照顺序进行解析操作。

当然，单线程管道也支持配置是否当作路径使用。

# 五、对外数据接口

当前的边缘网关系统支持对外输出设备的数据，或是第三方软件系统来主动请求指定设备的数据，本边缘支持如下的几种数据接口，还支持第三方来调用设备的方法接口信息。

在数据请求的时候，可以选择 WebApi 协议，或是 MRPC 协议，参考下面的数据信息。

# 5.1 基于 MRPC 协议

我们如何自己通过代码获取到这些数据呢？网关内置实现了基于 MRPC（基于MQTT 协议演变而来的协议，支持远程调用，结果返回） 的功能。如果需要数据测试，查看当前的进行一些基本的读写操作，就需要打开另一个测试软件（ HslCommunicationDemo ）软件，打开之后选择 MQTT 分类的 MRPC Client，在连接上边缘网关系统时，就可以查看接口列表。

About 简体中文English博客 MesDemoAPl文档 Version: 10.0.2 全国使用分布  
所有设备列表保存列表 起始页 MQTT同步客户端（(RPC远程..×  
Melsec Plc[三菱] 博客地址：https://www.cnblogs.com/dathlin/p/11631894.html 使用协议：MQTT RPC  
SIE Siemens Plc [西门子]  
ud Modbus Ip地址：127.0.0.1 端口号：521 用户名: admin 密码： 123456  
Inovance Plc[汇川 断开连接  
田-om Omron Plc[欧姆龙] 客户端标识: 2输入ip，端口，用户名，密码  
LS LSis Plc  
Keyence Plc[基恩士] Api List: (RPC 接口列表) 刷新 主题： Edge/DeviceData Called Count: 5259 Spend Total Time: 0.17  
Pansrni ly韦尔 Rpc Apis  
Ek d xmlDeviceNndes 上传数据：  
x B “data': " 4 输入参数 5271  
读取 读取发100% 读取(下载1M) 73 74TimeOut 接收主题：0 Apis Information 耗时： 9 ms由 Business- 9 Mqtt Client Topics 已接收100% O Text O Xml O Json 清空-9 Mqtt RPC Client 接收数据：11 q选择MRPCClientY9 Mqtt File Client  
WS WebSocket  
Http 如果读取成功，数据会显示在这里，如果失败，会弹窗  
由Robot[机器人]  
CNC[数控机床]  
Sensor[传感器]  
Freedom[自由协议]  
Debug About[调试技术]  
Hsl Protocal[HSL协议]  
ui RarCadal妇码1 4

比如我们需要得到设备1 的全部数据，我们可以在 data 中输入”/工厂一/车间A/设备A01”，然后点击读取，这时候就可以获取到数据

Ip地址: 127.0.0.1 端口号： 521 用户名： admin 密码: 123456 断开连接   
客户端标识：   
Api List: (RPC 接口列表) 刷新 主题： Edge/DeviceData Called Count: 5029 Spend Total Time: 2.00   
Rpc Apis [签名] OperateResult<JToken> Edge/DeviceData (String data) Admin Edge [注释]获得指定设备路径的数据，可以用来获取所有的数据 XmlDeviceNodes CommunicationTest 上传数据： A DeviceData 'data':"工厂-/车间A/设备A01" 5081 选择DevideDatgyDta 2 data输入设备的唯一路径信息 WriteData 白 DeviceName DeviceSerialPorts 3 点击读取 已发送100% 0 0 0 0 0 0 DeviceCommunicationTest 读取 读取(上传1M) 读取(下载1M) GetMethodByDeviceType 耗时： 13 ms GetMethodByDevicePlugins 接收主题：0 Apis Information GetMethodlnfoByDevicelD 已接收100% O Text O Xml O Json CallDeviceMethod 接收数据： ！ GetDeviceExampleAddress "_startTime': 2021-08-17 22:11:19.872", Plugins "_name':"设备A01', 由 Business "_description":"此设备安装在角落，编号0001",   
且Topics “_config':"[MelsecMc3E][工厂一/车间A/设备A01][127.0.0.1:6000]". _success': 9760, 由工厂 _failed': 0, "_activeTime": "2021-08-17 22:52:22.281", "_captureSpendTime': 1, 4 结果内容区 "_deviceStatus': true, "_onlineTime': "2021-08-17 22:11:21", "_failedMsg': ", "_alarmCount': { "AlarmCount': 2, “Hint': 2,

我们得到了设备 A01 的全部数据，那么可能对于我们来说，我们只想获取到产量数据怎么办？

![](images/ed73ab856915fc16b58cce60c09b53a10ba19f96e194d2842687fdeb01d84c36.jpg)

我们看到获取到了正确的数据，那么我现在需要获取到车间A 的全部设备的数据

Api List: (RPC 接口列表) 刷新 主题： Edge/DeviceData Called Count: 5029 Spend Total Time: 2.00   
Rpc Apis [签名] OperateResult<JToken> Edge/DeviceData (String data) Admin Edge [注释]获得指定设备路径的数据，可以用来获取所有的数据 XmlDeviceNodes CommunicatonTtest 上传数据：（"data" 厂-/车间A/” 5081 GetRamUseHistoryData 1 1 输入车间A的唯一路径信息 BrowseDeviceDataNodes WriteData DeviceName DeviceSerialPorts 2 点击读取 已发送100% 0 0 0 0 DeviceCommunicationTest 读取 读取(上传1M) 读取(下载1M) GetMethodbyDevicepigins 接收主题：0 Apis Information 耗时： 7 ms GetMethodlnfoByDevicelD 已接收100% O Text O Xml O Json CallDeviceMethod 接收数据： GetDeviceExampleAddress "工厂-/车间A/设备A01'{ Plugins '_startTime": "2021-08-17 22:11:19.872", 由 Business _name':"设备A01',   
白Topics "_description':"此设备安装在角落，编号0001'， _config":"[MelsecMc3E][工厂一/车间A/设备A01][127.0.0.1:6000]", 由工厂 _success': 11576, "_failed': 0, \*_activeTime': "2021-08-17 22:59:58.432", "_captureSpendTime': 1, 3 显示了车间A的所有设备数据信息 "_deviceStatus': true, 包括设备A01，设备A02等等 \*_onlineTime': "2021-08-17 22:11:21", "_failedMsg":" \*_alarmCount': { "AlarmCount': 2, "Hint': 2, "Warn': 0, "Frror".n

我们看到，在 data 里面，以 “/” 结果表示分类路径，如果是获取网关全部设备的数据，那么”data”“”

如果我们需要获取报警的信息，查看网关的所有的报警，则可以通过 Business 接口里的 GetAlarms子接口来实现相关的功能：

![](images/3029e3404d488537d66831d3b69d5e3427db1615b4715706fa4ae62c5717de5d.jpg)

我们也可以只获取当前边缘网关服务器有多少个报警，只关心数量，报警等级，而不需要关注实际内容，通常在一些显示统计信息的页面。可以使用接口 GetAlarmJsonCount，如下：

![](images/1ed481db101d0e80b7cd9b11ac66aa1d38060c6c4152531584b7c0ab1183fd1b.jpg)

我们也可以对边缘网关的服务器的方法接口进行测试，我们选择Edge/CallDeviceMethod 测试接口，然后对 工厂一/车间A/Fanuc 机器人设备 的 ReadFanucData 方法接口进行调用，实际测试如下：

![](images/f015d5cd722ce999ba620636d334cdb6286ae8cdd9df18d033f5e8c1edc5d062.jpg)

我们需要对某个设备的数据进行写入操作时，可以使用 Edge/WriteData 接口来实现相关的功能，例如我们写入 工厂一/车间 A/设备 A01/产量 这个标签，值为100，可以这么操作

Api List: (RPC 接口列表) 刷新 主题： Edge/WriteData Called Count: 0 Spend Total Time: 0.00  
□Rpc Apis [签名] OperateResult Edge/WriteData (String data,String value)Admin[注释]写入设备指定的数据，只能针对配置的节点写入数据EdgeXmlDeviceNodesCommunicationTest 上传数据： ！"data':"工厂一/车间A/设备A01/产量',GevicemUseHistoryDataBrowseDeviceDataNodes 2 输入节点数据，值数据WriteData选择写入数据接口HaiP 3 点击读取 已发送100% 0 0 0 0白 DeviceCommunicationTest 读取 读取(上传1M) 读取(下载1M)@ GetMethodByDeviceTugins 接收主题：0 Apis Information 耗时： 5 msGetMethodlnfoByDevicelD 已接收100% O Text O Xml O JsonCallDeviceMethod 接收数据：GetDeviceExampleAddressPlugins 如果写入失败，会弹窗提示失败，此处成功，没有信息返回由 Business  
Topics_log由工厂

# 5.2 使用代码来读写数据

如果我们需要在自己的项目里写代码来实现上述的功能，比如读取设备A01 的数据？需要使用我们提供的组件来进行数据访问，对于设备 A01 来说，因为返回 json 对象，所以读取json 对象

MqttSyncClient·mqtt·=:new-MgttSyncClient(-new-MqttConnectionOptions(?) IpAddress = "127.0.0.1" Port = 521, Credentials ·=·new-MqttCredential(- ) UserName·=-"admin" Password·=: "123456" ConnectTimeout·=·2000   
}）;   
mqtt.SetPersistentConnection(·);·//·设置长连接   
OperateResult<JObject>·read·=·mgtt.ReadRpc<JObject>( "Edge/DeviceData", new { data =""工厂-/车间A/设备A01"-}-);   
if-(read.IsSuccess) -Console.WriteLine(-read.Content.ToString(-) );   
mqtt.ConnectClose(·);

如果我只是读取个产量的数据呢？我们知道产量是一个整数。这时候我们可以使用泛型的方式获取到正确的数据。

MqttSyncClient-mqtt = new-MqttSyncClient(-new-MqttConnectionOptions( -) IpAddress =: "127.0.0.1" Port = 521, Credentials -=·new-MqttCredential( - ) UserName·=- "admin", Password·=:"123456" ConnectTimeout·=- 2000   
mqtt.SetPersistentConnection(·);://·设置长连接   
OperateResult<int>·read·=·mqtt.ReadRpc<int>( "Edge/DeviceData", new { data = "I厂-/车间A/设备A01/产量"-} );   
if (read.IsSuccess)-Console.WriteLine( read.Content.ToString( ) );   
mqtt.ConnectClose(·);

如果是写入产量的数据，可以调用写入的接口：

OperateResult<string> read·=·mqtt.ReadRpc<string>( "Edge/WriteData", new { data =""工厂-/车间A/设备A01/产量", value =· "100"·}·); if (read.IsSuccess) -Console.WriteLine(-read.Content.ToString(-) );

如果是请求设备的报警数据信息，因为报警信息是 json 数组，可以这么使用：

OperateResult<JArray>·read =·mqtt.ReadRpc<JArray>( "Business/Alarm/GetAlarms", new·{ data = }）;   
if-(read.IsSuccess)-Console.WriteLine(-read.Content.ToString(-) );

如果需要调用设备的方法的情况：

OperateResult<JObject>·read·= mqtt.ReadRpc<JObject>(- "Edge/CallDeviceMethod", new {·data = "工厂-/车间A/Fanuc机器人设备", method = "ReadFanucData", parameterJson =\*""-}·); if·(read.IsSuccess)·Console.WriteLine(·read.Content.ToString(·) );

需要注意的是，在实际调用接口之前，必须确认接口的数据返回类型，然后传入合适的数据类型，如果数据类型不一致，可能发送转换异常，返回错误的调用结果。

# 5.3 浏览器访问数据

除了上述的 MRPC 获取设备的数据之外，为了支持任意的第三方的设备，支持 Webapi 的形式获取设备的数据，也就是说，哪怕是网页，也可以获取到我们的数据，那么支持的 WebApi 哪里可以查询到？答案还是 HslCommunicationDemo 程序。

Melsec Plc [三菱] 博客地址：http://www.hslcommunica tion.cr 使用协议：web api 保存连接   
-SIE Siemens Plc [西门子]   
Mud Modbus   
Inovance Plc[汇川] Ip Address127.0.0.1 Port522 UseHttps Nameadmin password 123456 close   
由-om Omron Plc[欧姆龙] 2输入IP地址，端口，用户名，密码，点击open ttps, the port is default 443   
-t5 Ssis nc pl 恩gt Api List: (RPC 接口列表) 刷新 Api: Edge/DeviceData?data=工厂-/车间A/设备A01 4输入参数信息d Count: 0 Spend Total Time: 0.00   
签edt   
® CovieunicatinTtet Body:   
Furpk ld -XIN XinJE Plc[信捷] ③ 选择要操作的接ydades WriteData DeviceName 0 0 0 。 0   
etaplcd DeviceSerialPorts 5点击读取   
ID Card[身份证] DeviceCommunicationTest GET 读取 none Apis Information 耗时： 10 ms   
O Text O Xml O Json 潜   
Http "Content': { Http Web Server @GetDeviceExampleAddress "_startTime": \*2021-08-18 08:50:52.582, Http Web Client Plugins _name':"设备A01"   
0 选择HttpWeb Client Business eipt编   
申-CNC[数控机床 _success': 25607,   
Sensor[传感器] "_failed': 9,   
Freedom[自由协议] _activeTime': "2021-08-18 10:38:28.459\*, 6 返回的结果信息   
Debug About[调试技术] _captureSpendTime': 1,   
Hsl Protocal[HSL协议] '_deviceStatus': true,   
- BarCode[扫码]   
-Instrument [仪器仪表] _alarmCount': {   
-TOL Toledo [托利多] “AlarmCount': 2,   
-Control [控件库] Hint': 2,

如果你希望代码来实现调用 webapi，可以查找各个语言的调用webapi 的方法，这里需要注意的是对于每个API 区分 GET 操作和 POST 操作，如果是 GET 的 API，需要将参数传入到 url 的地址中，如下所示：

Edge/DeviceData?data=工厂一/车间A/设备A01

Api List: (RPC 接口列表) 刷新 Api: Edge/WriteData Called Count: 0 Spend Total Time: 0.00  
□且Rpc Apis[签名] OperateResult Edge/WriteData (String data,String value)由 Admin[注释]写入设备指定的数据，只能针对配置的节点写入数据Edge-XmlDeviceNodesBody:-CommunicationTest "data':"工厂一/车间A/设备A01/产量",DeviceData "value': "100"-GetRamUseHistoryData 2这是一个POST接口-BrowseDeviceDataNodes 所以在body里传参-WriteData选择WriteData接口 0 0 0 0 o 0 03点击读取deicetoynivatinTtest POST 读取 none √ Apis Information 耗时： 6 msGetMethodByDevicePlugins Body传入参数，如果是GET模式的话，参数需要通过地址传送，例如 O Text O Xml O Json 清空GetMethodlnfoByDevicelD GetA?id=5&name=job-CallDeviceMethod Response:PgetDeviceExamplAddress Erecge 4 返回调用结果信息田 Business

既然支持 GET 操作，那我们打开浏览器看看是否可以获取到数据，我们在浏览器的地址栏里面输入：http://127.0.0.1:522/Edge/DeviceData?data=工厂一/车间 A/设备 A01

![](images/53f560ff2c00e576efafb04ca01f9e156e433f49a438df7997a416e252b3ca6a.jpg)

此处提示需要输入用户名和密码的信息，那么我们就输入默认的：admin 和 123456我们使用谷歌浏览器进行了测试， 同理我们只需要访问一个数据的情况是，举例只访问 产量地址栏里输入：http://127.0.0.1:522/Edge/DeviceData?data=工厂一/车间 A/设备 A01/产量

![](images/1d268883f7692e6f234d33e672cff09cacba4c53cb7a41c4b3a1ea64992388bb.jpg)

![](images/4a1384e98c53b320f410bf5399020ca5f914f5ec588b087b1ddc74810f01d84c.jpg)

我们再使用 POSTMAN 软件来测试下。

![](images/c990c00f4568061d01609d15882467e342c93bf9a437bcb12b3a8ea2cbcbdb85.jpg)

我们看到可以获取正确的数据，当然地址路径支持的格式和一开始说的格式是一样的，也是支持读取单个的数据，支持同一分类路径下的多个数据。

# 5.4 基于MQTT 的数据订阅及发布

上面小节展示的都是我们主动从网关获取数据的方式，无论是 MRPC 接口还是 WebApi 接口，那么有没有网关的某个数据变了，自动推送给我们的功能呢？答案是有的，还记得我们之前在添加产量数据的时候，有个数据权限的选项吗？

![](images/112ab22092e728c215162f1871c98473a6e0732d18e9090e7bf2f8107561d42c.jpg)

我们打上勾之后，将当前的配置参数写入边缘网关系统服务器，然后重启服务器。  
在我们真正开始订阅数据时，那么问题来了，我们怎么指定刚才的数据需要订阅的Topic 是什么？其实就是产量的唯一数据标识：工厂一/车间A/设备A01/产量  
如果实在不清楚，可以打开 HslCommunicationDemo 来进行确认！

![](images/2b3011330a2591052e86cbe29cd16fc4b0d07982ca72822f20d2abf58582b8b5.jpg)

那么我们来测试一下是否真的可以订阅：打开 MQTT 客户端，输入 Topic 信息，然后点击订阅的操作，我们就可以看到这个数据订阅成功了。

![](images/3b8480e76e4a19985e6b8520dda53b6583e91e67c850ca8a7c4745bcc1271cc8.jpg)

这么操作的话，是一个个的数据开启订阅模式，我想一次性开启所有数据的订阅功能，可不可行？当然是可以的，因为数据量太多的话，订阅是相对比较消耗性能的，所以这里的数据订阅默认是关闭的，您可以根据自己的实际情况选择是否开始所有的订阅。

具体的配置在网关的服务器参数设置界面进行配置：

保存，重启网关。然后看看有哪些数据是可以订阅的。从V1.6.0 版开始支持数组的订阅操作，只支持单个标量的数据，或是结构体内的单个标量数据的订阅操作。

![](images/c9c7298d0202eb6a3d1bbe59d019a18152e3805fc8843b5c7365943263649275.jpg)

![](images/56bb2602509e6ddcf47f171f8ea7fbb75746286921ef17dd988bf1f0ac57e7b6.jpg)  
那么订阅的主题需要改为：工厂一/车间 A/设备A01/#

# 通配符订阅：

在上面的信息里显示设备 A01 里面有好几个数据，如果我都想订阅，管它里面的数据有几个，只要是这个设备的数据都想订阅，那就可以使用通配符来订阅，关于通配符的格式遵循 MQTT 协议的V3.1.1 版本规定，具体参考：htp:/public.dhe.ibm.com/software/dw/webservices/ws-mqt/mqt-v3r1.html#apendix-a

HslCommunication 测试工具 □ X  
About简体中文English 博客 MesDemo API文档Version:10.2.2 全国使用分布 Ram: 23.5MB Timeout:0 Lock:0 Wait:0  
所有设备列表保存列表 起始页 关于授权 MQTT同步客户端(RPC远程.. mc虚拟服务器【数据支持， MQTT客户端×  
Melsec Plc[三美 博客地址： https://www.cnblogs.com/dathlin/p/11631894.html 使用协议： MQTT 保存连接  
-SIE Siemens Plc [西门子]  
MModbus Ip地址: 127.0.0.1 端口号：521 接收超时：5000 KeepLive : 100 RSA加密（需要服务器支持) 断开连接  
Inovance Plc[汇川]由-om Omron Plc[欧姆龙] 客户端标识： 用户名： admin 密码： 123456  
-LS LSis Plc  
- Keyence Plc[基恩士] Topic: 工厂一/车间A/设备A01/# 主题信息 压力测试  
-Pan Panasonic Plc[松下]  
@AllenBrandly[罗克韦尔] Payload: 1 带通配符的订阅  
BE Beckhoff Plc[倍福]GEPlc[通用电气]  
Yaskawa Plc[安川]  
yamatake[山武]  
Ke RKC[理化] 2 点击订阅按钮  
T Fate Pld] □ Retain? 最多一次 最少一次 正好一次 只发不推送 订阅 取消订阅 追加显示 O Text O Xml O Json 清空○要盖显示  
-XIN XinJE Plc[信捷]  
Yokogawa Plc[横河] receive:   
ADelta Plc[台达] Topic[工厂一/车间A/设备A01/产量]100  
ID Card[身份证] Topic[工厂-/车间A/设备A01/温度]0  
Redis Topic[工厂一/车间A/设备A01/提升机1.提升个数]0 3 我们看到设备A01下面的四个数据全部订阅成功  
-Y9 MQTT Topic[工厂一/车间A/设备A01/状态]0-Y9Mqtt ServerY9 Mqtt Client-¥9  Matt RPC Client

同理，我想订阅车间A 的所有的数据，那么主题为：工厂一/车间A/#，如果需要取消订阅，同样使用该主题来取消订阅即可。如果不需要通配符功能也可以配置取消。（当设备数量很多，且TOPIC数量很多，达到万以上级别时，取消通配符功能，可以感受到 MQTT 性能明显的提升）

在系统参数配置里。

![](images/c91c36a2b52b23cd10f3661697915d6d629010f00c74e845fc7fc8649db41a62.jpg)

# 发布数据写入到设备：

上面的操作都是获取数据的，此处也可以通过 MQTT 的发布操作，将数据写入到设备中去。我们还是上面的数据标签为例子：

![](images/1779033bc671a6fdf9e7e113abf24099ccf7d99fea7eec380fd3adc6e8254ffb.jpg)

# 现在回到网关上查看数据：

![](images/37e3072121b6497d8cfad94897703b37c690208f64ed0a2809941850472a5627.jpg)

发布主题写入设备功能，暂时只支持单个标签操作，支持写入数据的标签，如果是short[3]的标签，字符串输入[1,2,3] 即可。

# 5.5 数据上传到 Redis

之前演示了如何获取到设备的数据，如果需要将数据主动上传到另一个电脑，或是云服务器，那么可以选择上传到 Redis，只需要设置服务器的 Redis 的基本信息。

![](images/79c0147a79ddf137fc8c70aceab658b0a0224af97c761b2cbe997060e2c23bfc.jpg)

![](images/45c7aa6983f05e3101385873726d4c4a4b627af721f38492d963347ca9c7ef15.jpg)

设置好之后，点击保存，然后重启服务器的程序即可。

![](images/ee9aad986ba9399cd46c3eb76b17c6b627f3c109d77a0b15257f00525a100d08.jpg)

# 5.6 数据发布到远程 MQTT 及写入

本节演示将数据上传到远程服务器的 MQTT，和Redis 比起来，优点是服务器的程序可以自己开发，收到数据后第一时间触发业务，Redis 的优点是服务器接收数据的不需要编写，而且具有很好的数据可视化效果，Redis 还可以用于网关的时间同步。

![](images/7760001ded895c56e5edd6a65bf04b426bedf89c6366e523eb445f6f82011931.jpg)

设置好之后，点击保存，然后重启服务器的程序即可。

我们启动测试的MQTT 服务器，把接收到的数据打印出来，我们可以看到：

C:Windows\system32\cmd.exe □ ×431572DD8C1903-1018:50:46./车间A/DTSU6606-RTUedlsg "Tim 端id:Edge:431572DD8C19-03-1018:50:46.144工厂 /车间A/Fanuc机器人设备tartTime" "2022-03-1018:50:19" name":"Fanuc机器人设备” _config":"[RobotFanucInterface][工厂一/车间A/Fanuc机器人18:50:19 1:6i1e8 SocketException->由于目标计算机积极拒绝，无法连接。 127.0.0.1:60008肖息的客户端id:Edge:431572DD8C1920 22-03-1018:50:46.144工 温erttretrertrefa1s [true,false,false,false,false],"状态":3]572DD8C19-03-10 18:50:46. 144工 /车间A/ 设备A02消息 _startTime 2 failed":0,"

1） 单网关模式：一个边缘网关服务器所有的数据使用一个 Topic 主题，主题为网关的 ID 信息，不同的网关使用不同的 Topic 来区分。  
2） 单设备模式：网关里的每个设备使用一个不同的Topic 主题，每个设备的主题都是设备名称信息，ClientID 是边缘网关的唯一 ID 信息。  
3） 数据标签单主题：每个数据都是不同的 Topic 信息，clientID 是边缘网关的唯一 ID 信息。

也支持从MQTT 服务器进行反写操作，网关作为客户端，会订阅”[网关名]/WriteData”的主题，例如上述的网关服务器我们想要写入产量的数据，如下：

图边缘网关系统客户端 一 □ ×  
文件(视图(M) 工具(T)关于(A) 更新服务器 启动测试的Mqtf服务器  
本地网关 -ec  
服务器列表 口 设备监视界面 采集信息-本地网关  
+c 设备名称：Edge:B33520B93357 开始运行时间：2023-11-12 21:38:37 Version: 1.7.2 4 01①。83  
本地网关[127.0.0.1] 设备4报警2工厂 设备名称：设备A01 设备名称：设备A02 设备名称：Fanuc机器人设备车间A 活动时间：2023-11-12 21:48:10 活动时间：2023-11-12 21:38:37 活动时间：2023-11-12 21:38:37设备A01 采集耗时：146ms 采集耗时：0ms 9.6分钟 采集耗时：2005 ms 9.6分钟产量 成功次数：2288 失败次数：1 成功次数：0 失败次数：0 成功次数：0 失败次数：282温度 02 ×。 ×o 0。 ×。 xo Call Method[Data]failed:Socket Exception -\*>[]IO设备名称：DTSU6606-RTU）提升机1 活动时间：2023-11-12 21:38:37集时：5014ms 失败次数：113 9.6分钟设备A02 Request [电参数类] failed: Time out: 5000中Fanuc机器人设备由DTSU6606-RTU 数据信息设备信息方法接口报警信息OEE信息离线信息设备日志车间B工厂二 节点名称： 工厂一/车间A/设备A01数据名称 值(Value) Unit 类型 权限 描述产量 610 0 想通过远程的服务器写入产量数据 Int16 ReadWrite 一次完整的数据请求温度 5 Int16 ReadWrite10 [ false，false,false，false,false,false,false,false,fal. Bool Read提升机1 （“提升个数"：0} Struct Read 这是提升机1的数据信息报警 [true,true,true,false，false] Booll ReadWite 一次完整的数据请求状态 5 Int16 Read 一次完整的数据请求

我们这时候启动一个 MQTTClient，然后往配置界面上传的远程 MQTT 服务器上发布一个主题数据，如下所示：

HslCommunication 测试工具 □ ×  
About 语言 报文日志 官网 APl文档 Doc Version:11.7.0 Active 全国使用分布 Ram: 54.7MB Timeout:0/0 Lock:0 Wait:0  
设备列表 中 起始页 MQTT同步客户端（RPC远程... MQTT客户端×  
Melsec Plc[三菱] 博客地址： https://www.cnblogs.com/dathlin/p/11631894.html 使用协议： MQTT 保存连接  
-SIE Siemens Plc [西门子]  
ud Modbus Ip地址：127.0.0.1 端口号：1883 接收超时：5000 KeepLive:100 □RSA加密 (需要HSL服务器) 断开连接  
Inmron P 客户端标识: 1先输入IP地址，端口号，其他连接参数根据实际情况输入 密码: 遗嘱  
5 LSis Plc □ SSL/TLS CA File: Select □ SSL Secure ?(server check)  
-Keyence Plc[基恩士] Subscribed: (已订阅) 2输入写入的主题数据Topic: Edge:B33520B93357/WriteData 主题信息 订阅 取消订阅 子窗体订阅  
Pan Panasonic Plc[松下]  
AllenBrandly[罗克韦尔] Payload:{  
Gechf □ Hex? alt: 工厂-/车间A/设备A01，【输入JSON格式的参数，写入哪个数据，值是多少  
Yaskawa Plc[安川]  
yamatake[山武]  
RKe RKC[理化]  
FAT Fatek Plc[永宏]  
Fyjir Pt □ Retain? AtMostOnce 发布 8显 O Binary O Text O Xml O Json  
-XIN XinJE Plc[信捷] Receive:  
田MegMeet Plc[麦格米特] 4 点击发布  
Yokogawa Plc[橫河]  
Toyota Plc[丰田]  
Delta Plc[台达]  
ID Card[身份证]  
Redis

# 然后我们回去看看这个值的情况。

边缘网关系统客户端

文件(F)视图(V）工具(T)关于(A)更新服务器 启动测试的Mqt服务器  
本地网关 ·。c  
服务器列表 设备监视界面×采集信息-本地网关  
+c 设备名称：Edge:B33520B93357 开始运行时间：2023-11-1221:38:37 Version: 1.7.2 64 01①08  
□本地网关[127.0.0.1] 设备4报警2工厂- 设备名称：设备A01 设备名称：设备A02 设备名称：Fanuc机器人设备车间A 活动时间：2023-11-12 21:57:53 活动时间：2023-11-12 21:38:37 活动时间：2023-11-12 21:38:37中设备01 ：146ms 失败次数：1 采集耗时：0ms 失败次数：0 采集耗时：2001 ms 失败次数：570温度 2 ×。 Xo 0。 ×。 ×o Call MetodData]failed: Socket Exception ->设备名称：DTSU6606-RTU：3设备A02 Request [电参数类] failed: Time out: 5000-Fanuc机器人设备由DTSU6606-RTU 数据信息 设备信息方法接口报警信息OEE信息离线信息设备日志车间B工厂二 节点名称： 工厂一/车间A/设备A01数据名称 值(Value) Unit 类型 权限 描述产量 1234 0 值已经被修改 Int16 ReadWrite 一次完整的数据请求温度 Int16 ReadWrite[ [false，false， false，false, false, false,false, false， fal. Bool Read提升机1 {“提升个数"：0} Struct Read 这是提升机1的数据信息报警 [ true,true, true,false, false] Bool ReadWrite 一次完整的数据请求状态 Int16 Read 一次完整的数据请求

# 5.7 数据和物联网平台 JetLinks 对接

本网关自 v1.7.2 版本开启可以和物联网平台 JetLinks 对接，使用了基于MQTT 的 JetLinks 官网协议进行对接操作。协议地址说明：https://doc.v1.jetlinks.cn/basics-guide/jetlinks-protocol-support.html ，关于 MQTT 上传配置如下：

![](images/2b7a9536f4dd1814adccf890e6c64a042006e455caa66d324936e8aedf802994.jpg)

为了和物联网平台的物模型进行匹配，我们需要在配置设备信息的时候，关注一个设备属性： 主题前置信息，以下举个例子：

![](images/d781a0d0c25bfb111bab529c6fe4c4ed163421dcb39a2477c161a70e51a87c79.jpg)

# 物模型的点位信息如下：

![](images/7382e69b9f7f1b8b6c8b9696ccf44dbd7adbd6ab91f0c67c63756db45977f23d.jpg)

根据 JetLinks 的官方手册文档，泵 2 设备的访问路径是 /pump_2/234，然后我们在网关里新增设备的时候需要进行匹配操作：

![](images/b25d948a0eb46db8142b46d23c82858713a3e99400789c57dd37ef5db7b251da.jpg)

除此之外，我们还要创建标签名称一样的点位，如这里所示：

![](images/86d8e029228449d83c0069ac8b485734458b6e273bdde04809e4f30495b9edb3.jpg)

一切准备就绪后，就可以下载到网关服务器，然后断电重启操作了，我们就在平台上看到了点位的变化，并且可以执行反写和强制刷新操作的。

![](images/3ac8f015c5879658766f4377161149d787602f3d2d727c8ea0836844bfccaf42.jpg)

当然也支持部分的设备功能调用，例如网关里的西门子 PLC 提供了读取订货号的方法，那么也可以通过 JetLinks 来实现调用操作，先定义好这个功能方法。

![](images/52571836e0cf8118e8ec3ac06ee15ce5b919ae41418071675e5d0c3722581c33.jpg)

然后回到设备功能里，进行点击执行的操作，就可以在执行结果里，看到获取的订货号。

![](images/682839558242f33214b668089b253f79d60587df50ac96cf604a85257364680b.jpg)

# 5.8 设备启用 MRPC 服务

以上的种种方式可以让我们获取到数据信息，那么我们是否可以对配置的 PLC 进行任意的读写操作？而不仅仅是配置好的读取数据而已，答案是当然可行的。

![](images/968a71dee893c17b422724d32678baa34758e4afb5efefcf736f6059598e83e3.jpg)

我们回到服务器进行重启服务，记得是管理员重启。

这时候设备A01 已经支持远程 MRPC 进行读取写入数据了，这时候API 接口在哪里查看，答案还是之前就提到的HslCommunicationDemo 软件，打开 MRPC 客户端，就可以浏览到。

HsICommunication 测试工具 □ ×  
About 简体中文 English 博客 MesDemo APl文档 Version:10.1.2 全国使用分布 Ram: 43.6MB Timeout:0 Lock:o Wait:0  
所有设备列表保存列表 起始页 mc虚拟服务器【数据支持，. Fanuc虚拟服务器 MQTT同步客户端（(RPC远.. Mqtt客户端 MQTT同步客户端（(RPC远程...×  
-K Keyence Plc[基恩士] > 博客地址: https://www.cnblogs.com/dathlin/p/11631894.html 使用协议：MQTT RPC 保存连接  
-Pan Panasonic Plc[松下]  
@ AllenBrandly[罗克韦尔]  
申BE Beckhoff Plc[倍福] GE Plc[通用电气] 客户端标识： Ip地址：127.0.0.1 端口号：521 用户名： 2设置好参数信息 admin 密码: 123456 断开连接  
Yaskawa Plc[安川]  
- yamatake[山武] Api List: (RPC 接口列表) 刷新 主题： 工厂一/车间A/设备A01/ReadPlcType Called Count: Spend Total Time: 0.00  
甲A Fui p Rpc Amin A [签名] OperateResult<String> 工厂-/车间A/设备A01/ReadPlcType()  
-XIN XinJE Plc[信捷] Yokogawa Plc[横河] Edge Plugins [注释]读取PLC的型号信息，例如 Q02HCPU  
Delta Plc[台达] Business 上传数据：0 4如果有参数，填入参数信息  
ID Card[身份证] 工厂  
Redis 车间 找到需要操作的接口  
Y9 MQTT 白设备A01M9Mqtt Server ReadByteArray9 Mqtt Client @WriteByteArrayY9Mqtt RPC Client -ReadRandom 5点击读取 已发送 0% 0 0 0 0 0 0 01 选择Mqtt RPC Client -ReadRandoms 读取 读取(上传1M) 读取(下载1M)  
甲S WesbSockile Ctient wrteBodaray 耗时：接收主题： Apis Information  
Http ReadTag 已接收 0% O Text O Xml O JsonHtp Web Sener -ReadTags 接收数据：-ReadExtend  
-Robot[机器人] ReadMemoryEFORT New [新版] ReadSmartModuleEFORT Pre[旧版] RemoteRun 6 获取结果K Kuka [库卡] RemoteStopK Kuka Tcp [库卡] -@ RemoteResetYRC1000 [安川] -B ABB Web -ReadPlcTypeErrorStateResetAB ABB Web Server RoadRonl  
< 》

当然也是可以使用代码来实现的，不仅仅是 C#的，JAVA 语言和python 语言都支持的，具体可以参考下面的文档：

https://www.cnblogs.com/dathlin/p/14094128.html代码的方式和之前获取数据接口的方式都是一样的。

# 5.9 设备启用 WebApi 服务

网关程序同样支持将 PLC 设备注册为远程服务器，从而进行任意的读写操作，还是回到设备配置的界面。选中设备A01 的节点，然后修改是否注册WebApi 接口的属性为 True，然后将修改后的配置信息写入到网关软件里去。

![](images/cd97752eeaff15cf63e216d91a7434ceb8ade30fb1ddcc3623ac9c2560340a4f.jpg)

参数写入网关之后，就可以重启服务程序了，然后还是使用HslCommunicationDemo 软件来查看相关的数据接口。

图 HslCommunication 测试工具  
About 简体中文 English 博客MesDemo APl文档Version:10.1.2 全国使用分布 Ram: 44.3MB Timeout:0 Lock:0 Wait:0  
所有设备列表保存列表 Fanuc虚拟服务器 MQTT同步客户端（RPC远程 Mqtt客户端 MQTT 同步客户端 (RPC远程.. FormWebClient× 1  
K Keyence Plc[基恩士] A 博客地址: http://www.hslcommunication.cn 使用协议：web api 保存连接  
-Pan Panasonic Plc[松下] 2输入正确的连接参数  
@AllenBrandly[罗克韦尔]  
申BE Beckhoff Plc[倍福] Ip Address127.0.0.1 Port 522 UseHttps Nameadmin password 123456 close  
GE Plc通用电气] if use https, the port is default 443  
中 yaskawk [ p Lit (RPC列表) 刷新 Api: 工厂一/车间A/设备A01/Readlnt16 Called Count: 0 Spend Total Time: 0.00  
-FAT Fatek Plc[永宏] [签名] OperateResult<lnt16> I厂一/车间A/设备A01/Readlnt16(String address)  
xj Pt [注释]  
Ta 选择需要的接口ody: "address': "D100" 4 输入参数信息 5设 eadBytAray− WriteByteArray 0 0 0 0 0 0 09Mqtt Client-ReadRandom ⑤ 点击读取按钮-Mqtt RPC Client  
营 白 8 HtpwebClient edende ererge 6 返回结果显示EFORT New [新H−RemoteRunEFORT Pre[日版]K Kuka[库卡] RemoteStopK Kuka Tcp [库卡] RemoteResetYRC1000[安川 ReadPtyesetAB ABB WebReadBool vA ABB Web Server>

如何使用 JS 代码来访问？可以参照如下的网页地址：

https://www.cnblogs.com/dathlin/p/14170802.html

大致的代码如下:

![](images/f4327cb12cbc7c5e913c27e3afbd44d2f97b94babc14be7d0b0f1d54f678ad8e.jpg)

# 5.10 Websocket 推送及写入功能

网关程序支持websocket 推送变化的数据。需要在配置数据的时候将节点配置为根据变化推送数据。可以从网页上直接订阅当前的某个标签的数据信息。

![](images/b092e7c08a47b2459fa4ee75c15ce3b1a9a9b5ae153bc73e5e40aa2a7a398ffb.jpg)

然后点击确认，将配置信息下载进网关服务器，重启网关服务器。

![](images/5fea15853ff1ba246301180749418c12ada19c0618911a9f40a08942b2177334.jpg)

我们这时候就可以开始订阅数据的操作，打开一个 websocket 在线测试网页，例如：

连接之后，需要先进行登录操作，登录的命令如下：

输入文件：{ "Topic": "", "Command": "Login", "Payload": { "Name": "admin", "Password": "123456" } }

![](images/4018b68a3c59571663f7f617a691c1a61a139ecdc6620e60dbba070e5ebd696c.jpg)

登录之后就可以发送订阅的命令呢？

![](images/68862b0a47b264358ef7ca827aca3c822301f0be6fed0fc878cf97ca19be4743.jpg)

输入文本：{ "Topic": "电表 2/电压", "Command": "Subscribe", "Payload": "" }对于订阅的操作，Payload 可以不使用，命令有三个："Subscribe", "UnSubscribe","Publish"如上图所示，我们可以看到已经成功订阅了相关的数据，并实时收到了返回的数据信息。如果需要订阅多个数据，则多发几次命令即可。Websocket 服务器也支持配置通配符的功能，逻辑和MQTT 一致。

# 使用发布数据写入到设备：

还是参考上面的网址：

![](images/cab406e6a9f7e48d39b0ac3d7e22a767f7f660b0252f66676ca034a15385b294.jpg)

输入文本：{ "Topic": "电表 2/电压", "Command": "Publish", "Payload": "202" }

然后点击发送，即可成功发布数据信息，如果标签是数组，例如 short[3]，那么发布数据的时候使用如下命令：{ "Topic": "电表 2/电压", "Command": "Publish", "Payload": "[1,2,3]" }

# 六、设备模板

设备模板部分解决什么样子的问题呢？我们可以举一些经典的例子，比如有很多一模一样的仪表接入了边缘网关，都是modbusrtu 协议，都是一样的地址，一样的解析，只是站号不一样而已，那么我们就要配置很多遍的设备，设备请求，设备解析，虽然结构体解析可以复用一部分逻辑，但是仍然需要进行大量的配置。而且灵活性也不行，将来设备升级了，所有的点位都需要配置一遍。

或是都是一样的三菱PLC 设备，点位都一样的，只是 IP 地址，端口号不一样，这时候使用设备模板就非常的合适。不仅可以降低配置的重复性工作，还能减少 XML 文件大小，提升传输效率和系统速度。

# 6.1 添加一个设备模板

我们先添加一个设备模板，这个和添加一个普通的设备是一样的。只是再模板节点下面操作即可。比如我们要添加一个 ModbusTcp 设备的模板。

![](images/602fbfc88e9694bc2cbcc8b0d03777904f7bf9a0771b851ca7fe5d8e563ef930.jpg)

设备部分的信息和之前的是一样的，然后继续添加请求以及相关的解析，我们按照假设进行了配置操作，您可以根据实际的情况进行按照实际配置。

![](images/ba4473a594748e36c32157dcb88ca96ea28b49de3b79d8054699dd552b332a25.jpg)

# 6.2 添加一个基于模板的设备

我们添加好模板后，就可以添加真实的设备了，在想要添加的节点下面添加模板设备，如下图：

![](images/3bf532b805be62e819c9467cea1f098805716923736c4641e9da9e1c0a750224.jpg)

![](images/6ab018111eccc42b7e125d6766302f716a4c40c8a9126fd21942d8128c2def52.jpg)

按照这个方式，可以添加基于模板的设备，添加设备后，不用配置任何的请求信息。我们添加 3 个设备后，然后保存并且重启边缘网关服务器。然后再看看效果。

![](images/ba63d41f44c474763787c392d8324ad4a2576774e1af465826e21257e55daeef.jpg)

基于模板的设备的采集以及解析都是一致的，这在实际的应用中，可以极大的提升配置的效率。

# 七、插件扩展

插件功能主要是两个核心目的：

1） 用于扩展目前在边缘网关系统里还不支持设备或是协议  
2） 标准化当前的电气设备的数据，报警，状态等

在介绍这两种经典的场景之前，需要简单的说下，如何开发一个网关支持的插件，需要新建一个 dll包，框架为.net standard2.1（可以跨平台运行，随边缘网关部署到 Linux 系统，Windows 系统）或是.net framework4.6.1（仅支持 windows 系统，仅支持部署到 windows）。然后新建一个类文件，固定名为：PluginsHelper.cs 需要包含插件的基础数据信息。

然后再开发两个类，一个是设备节点类，一个是设备实体类。对于设备节点类，可以继承自边缘网关组件(HslTechnology.Edge.dll)的 DeviceNode 类，并扩展其他自定义的属性，也可以完全的自定义操作。对于设备实体类可以继承自边缘网关组件(HslTechnology.Edge.dll)的 DeviceCore类，并实现数据通信操作，也可以完全自定义的类实现操作。本小节主要就是讲解下插件开发的基本概念，更详细的教程可以参考在线的文档：

http://www.hsltechnology.cn/EdgeDoc/HslTechnology

# 7.1 扩展新设备实现

比如我们需要扩展一个完全自定义的协议，例如opcda 的插件，该插件依赖 COM 组件，所以我们只能编译成.net framework4.6.1 的组件，而且只能是 X86 的版本，否则无法运行，添加PluginsHelper.cs 类文件，添加节点，实体类设备。

hame element 初始化的双 @ hugong.opcda 1C 0个引用IRichard.Hu，6 天前I1名作者，1 & Properties 19 public ·void· IniDevice( -XElement ·ele 》跖引用 5] app.config this.node·=· new·OpcDaNode(·elem V & C# DeviceOpcDa.cs this.myServer·=·new-OPCServer( & c# OpcDaNode.cs this.dictOpcItems =·new-Diction V Δ C# PluginsHelper.cs 24

开发好后，将插件放入边缘网关服务器的插件文件夹。重启边缘网关服务器即可。

![](images/d53f70b476cb0466e4f5c21fad4f75bb0ab305a48bd94af1b59a4c3b9699a9f8.jpg)

E:)》HslTechnologyEdge 》 Plugins

![](images/b06573dc8f4217a2f87ffb01678b0990a465b0795fa3bebc96063a3fd79f4f62.jpg)

重启边缘网关系统服务器软件之后，我们打开对应的客户端软件去连接。

![](images/666a863b7c25066a3a8640256d4fe5607f06dda6df2acd6e4a03d8861e0aded4.jpg)

![](images/9134d56ce1ddd8dda0f9de9a37dea732d1ed633804f2527a8b7d4a369a9c388e.jpg)

# 这时候就会需要输入插件设备的基本属性信息了。

![](images/c6cd92b024b691e8314383c727a15b05185f1f39dbfb3b3eb96ad76ede04aadb.jpg)

我们举个例子添加数据信息：

![](images/9b12880a870b21c6ddb6bffc842354566e38217614c14202111e0eae76dd2ea9.jpg)

采集之后，我们打开边缘网关的客户端来查看当前的设备信息，如下所示：

![](images/ea317136e46a188ca131a716e2f51ef5d99fd46c2c141a031c148a88f65f63f2.jpg)

# 7.2 用于标准化设备数据

本小节的本质其实也是新增一个设备，但是用途不一样，在边缘网关系统所支持的几乎所有的设备里，我们可以称为基础设备，包括所有的 PLC 设备，Modbus 设备等等，基础设备有什么特点呢？当我们添加一个设备之后，还需要添加大量的请求信息，否则没有数据，报警资源定义信息，OEE 资源定义信息，结构体定义信息等等。

但是实际的情况是，设备厂家生产的设备都是批量的，销往不同的生产企业，难道每个设备都需要进行配置一遍？每次都要添加所有的数据信息（可能一个设备就有几十个上百个数据）？不同型号间有些微小的差别怎么办？所有配置的数据节点如何进行版本控制？后续的升级？如果设备生产商需要对客户公开 PLC 的数据，但是不希望公开所有的 PLC 地址，这时候应该怎么解决？

在此，基于边缘网关系统的插件扩展功能，可以几乎完美的解决上述问题，比如我是设备厂家，我司生产的硫化机设备（控制器为三菱 PLC）相对比较标准，现在统一硫化机的数据接口，统一数据模型，给第三方提供标准数据接口，数据可视化接口。那么只需要创建一个硫化机的插件即可。

因为PLC 通信核心使用的是 HslCommunication.dll 来实现，所以我们可以放心的创建一个基于.net standard2.1 的跨平台库，可以运行在任意的框架之上。所以我们新建一个标准库即可，，添加 PluginsHelper.cs 类文件，添加节点，实体类设备。最关键的在实体设备类对象里，加入当前的已经确认的数据请求，这样当用户使用边缘网关插件添加设备时，就不需要额外的添加数据请求，就

# 可以自动采集相关的数据了。核心实现如下：

public:override:void AnalysisByBussiness( BusinessEngine business, UploadInfoConfig·UploadInfo ) //·此处举例需要添加本设备专用的报警信息，先实例化本地报警资源信息，如果担心相同的设备重复实例化报警资源 //·可以将下面的报警节点定义为静态对象，同种设备共享一个对象 this.localAlarmNodes ='new-Dictionary<string, AlarmNode>( ); //·添加一个整数的报警信息示例 this.localAlarmNodes.Add( -"报警", new NodeAlarmInteger(") Name"= "报警", AlarmDefinitions °='new· AlarmDefinitionNode[]  { new AlarmDefinitionNode(){ Code = 1, Degree = AlarmDegree.Hint, Name = "设备电流过载"}, new AlarmDefinitionNode(){-Code = 2, Degree = AlarmDegree.Hint, Name = "温度过低"}, ）； Requests.Add( new-ScalarReadRequest( -) Name = "报警", Description"=·"报警信息", Address = "D100" Length = -1, AlarmRelate =· "报警" ·//·关联上面的报警信息 DataTypeCode = RegularNodeTypeItem.Int16.Text, ForbidRemoteWrite = true, ·//·禁止远程写入操作 RequestInterval·= 1000, //·1秒请求一次 }; Requests.Add( ·new ScalarReadRequest(\*) Name = "温度", Description = "当前设备的温度信息", Address = "D101", Length = 5, DataTypeCode =· RegularNodeTypeItem.Int16.Text, ForbidRemoteWrite = true, ·//·禁止远程写入操作 RequestInterval:=: 1000, //·1秒请求一次 上 //·继续进行商业逻辑的解析操作

# 自动添加了 2 个数据，添加了内置的报警支持。在生成插件之后，和上个小节一样把dll 组件放入到Plugins 插件里，然后我们可以添加插件设备

![](images/58efcae1b641a3e9efa01446477e8ced90999ee1b280247d27325d193798eb79.jpg)

![](images/ed54b43c3a4a99df832d281f6892fdc877aade2888f3e5b7b5f558352e31bf16.jpg)

添加好设备之后，就可以直接将配置参数写入到边缘网关系统的服务器软件中，不用新增新的采集数据信息。然后重启边缘网关系统的服务器。

![](images/7f2ec02363a5f8813a41f1d3daf2552ca7a1391bc8d857ffc5399a9adf418cc9.jpg)

我们可以看到没有配置过数据请求，但是已经自动采集相关的数据了。在第三章和第四章里我们介绍过可以配置定时调用设备的方法接口，也可以使用代码来手动调用接口数据，那么在我们自定义的硫化机设备里，是否也可以增加标准化的设备接口方法呢？比如说设备的启动生产方法？设备的停止生产方法？答案也是可以的。下面演示我们在硫化机实体类里的方法接口代码：

//·提供几个测试的方法   
[HslMqttApi(·Description·=·"启动设备生产"·)]   
[RpcExtensionInfo( DataType:=: DataType.Method )] 0个引用|Richard.Hu，7天前l1名作者，2项更改   
public'async:Task<OperateResult>·StartProduction(-) return await·this.plc.WriteAsync(- "D200", 5 ); [HslMqttApi(·Description·= "停止设备生产"-)]   
[RpcExtensionInfo( DataType =· DataType.Method ·)] 0个引用|Richard.Hu，7天前l1名作者，2项更改   
public'async:Task<OperateResult>·StopProduction(^) return await this.plc.WriteAsync( "D200", 6 ); [HslMqttApi(·Description·=·"读取速度信息"·）]   
[RpcExtensionInfo(·DataType =·DataType.Int16·)] 0个引用|Richard.Hu，7天前l1名作者，1项更改   
public'async:Task<OperateResult<short>>ReadSpeed(?) return await ·this.plc.ReadInt16Async( "D200" \*);

定义好方法接口后，需要实现上述的特性才是真正的方法接口，实际启动停止的方法可以更加的复杂，带有更多的校验机制，此处仅仅是简单的举例。然后在设备界面我们可以看到方法接口信息，也可以进行实际的调用测试：

![](images/fa660a25148cd9ba3c73dbd702b3dc081bcd8ab14f9a1b16fb178a892fa56e02.jpg)

# 7.3 基于标准模板的插件

上面两小节的内容生成的插件依赖网关的dll 或是hslcommunication 组件，目前版本还有个致命的问题，当开发好了插件，将插件部署到网关系统里时，可以很好的运行。但是网关系统的相关的DLL 文件进行升级之后，就无法加载旧版本的插件了，需要重新依赖新版本的 DLL，然后编译一下。如果你的插件的设备时边缘网关自身就支持的话，则可以像模板一样，将模板独立为一个 DLL 文件，方便的进行分发，而且不依赖任何的 DLL，支持兼容的升级。

如何构建基于插件的模板呢？我们以德力西电表为例子，这个是典型的 ModbusRtu 设备，为了减少每次配置，故而适合做成插件DLL 文件，可以非常方便的分发多个网关系统。

第一步还是先添加PluginsHelper.cs 类文件，添加一些基本的属性，根据实际情况进行修改。

///·公司的名称信息///?</summary>0个引用|Richard.Hu，10天前I1名作者，1项更改public·static string Company { get; set; } □ "杭州胡工物联科技有限公司";///:<summary>///·插件的网址信息///·</summary>0个引用IRichard.Hu，10天前I1名作者，1项更改public·static-string-Http { get; set; }///·<summary>///·当前的插件的版本///:</summary>0个引用|Richard.Hu，10天前I1名作者，1项更改public static-string Version { get; set; -} = "1.0.0";///:<summary>///·当前公司的基本介绍信息///:</summary0个引用lRichard.Hu，10天前I1名作者，1项更改public·static·string·Description·{ get; ·set; -}///:<summary>///·当前插件的框架信息///:</summary>0个引用|Richard.Hu，10天前|1名作者，1项更改public-static-string- Framework { get; -set; -} =\* "standard2.1";///-<summary>///·获取或设置当前系统的语言信息，0：中文，1：英文，2：法语，3，俄语，4：阿拉伯语，5：西班牙语，6：日语<br·/>//ef///·</summary>///-<remarks>///·这个值将会由网关进行设置，传递给插件，插件在赋值数据及注释时，根据语言信息来完善当前的内容<br·/>////:</remarks>0个引用|Richard.Hu，10 天前l1 名作者，1项更改public-static-int-Language { get; set; }///:<summary>///·当前版本插件的发布日期<br·/>/// -The-release-date·of-the·current·version·of-the-plugin///:</summary>0个引用|Richard.Hu，10天前I1名作者，1项更改public·static-DateTime ReleaseDate { get; -set;-} =·new-DateTime( -2021, -8, 9-);///?<summary///·当前的品牌的自定义的图标信息，需要为一个16\*16大小的png,jpg格式的图片的内容///-</sun0个引用IRichard.Hu，10天前I1名作者，1项更改public-static-byte[]-Icon16 { get;set; HexStringToBytes(·@"89·5 08·06-00-00·00·1F·F3·FF00:00-09-70-48-59-73-00-00-0E·C4·00-00-0E -01 2B 1B-00-00-00·20-63 00:0000:00·80·83:00·00-F9-FF·00·00·80·E9·00 30·00·00·EA:60-00-00 3A:98:00·00-17-6F-92·5F00:00-00·A7-49-44-4 934EiD2·0E E8 08 1D·21 55-2920122-79·D5-ED-10-80-0A-6 A7:C9A6 CE·A8· E7·45·CD-D3-40·FB·23·1C·15·ED·C5 89. C2 28:06-B8:2F 6F 2F A0·B7·BC·B7·3D·1C·02·E206:6E-39-80-6E-43-8F-3B-1A-80-FC-7F-89 FB 85 38 31 53 6D-8E-0C 39 ·80·F5·2D·CC 8F-67-04AF:A7·CE·D5-E4·454A·CF·F9·33·00·19 BC-24-DE-26·F6·3B:63·00-00·00·00·49·45·4E·44·AE·42·60

接下来就是关键的返回模板的 XML 文件定义了。我们先在客户端上配置一个德力西电表的模板设备，然后将该设备导出到 XML 文件。

解决方案资源管理器 × Template1.xml X   
搜索解决方案资源管理器（Ctrl+ 0 Regularlaesti位eataodeoret-rbReoteitefsubti   
解决方案"HsITechnology"(14 个项目，共14个) <RegularScalarNode-Name="电压B" Description="单位V"-Index="2"-DataTypeCode="short"-Length=" ForbidRemoteWrite="false" Subscription="false Plugins <RegularScalarNode·Name="电压C' ·Description="单位V"-Index="4"-DataTypeCode="short"-Length="-1"-ForbidRemoteWrite="false"-Subscription="false hugong.delixi <RegularScalarNode·Name="电流A' Description="单位A"·Index="6"·DataTypeCode="short"·Length="-1"·ForbidRemoteWrite="false"·Subscription="false 》依赖项 <RegularScaladeDiptideDtpestgth-bReoteifbtie △ C# PluginsHelper.cs RegularScalaoda"Dipti单位de1"Dtaypedesrt"ngth-ForbdReoteitefseubsifa Template1.xml 1 新增XML文件 <RegularScalarodeN率"Desipti单位dex3"Dtaypedesrt"Legth-"FrbidRemteitefase"Subiptif 6 hugong.opcda </RequestNode> @ hugong vucaizer [</DeviceNode> 2 将配置信息粘贴到这里。 Info.txt

然后在 PluginsHelper.cs 类里关联 XML 的资源即可。

![](images/a1b22075d0db1ccee9c7af59632b3b0e640cb90cca97accf6fee804e344d521e.jpg)

编译生成dll（这里需要注意网关上面平台，这里就生成对应平台的 DLL），这样我们的模板类插件就完成了，可以看到我们生成的 DLL 很小。

![](images/1c93812142742dcdcb13af25e3d844174effee7c383fd918d198899d68475df8.jpg)

重启边缘网关系统，就可以添加基于模板的插件设备了。

![](images/59b0981e6fd725441dd7dbcd53f602d21da9cb77b84d17191192e3afc86d1fbd.jpg)

按照下面的所示，选择对应的设备信息。

![](images/2d10ae9ea6f581884625c4bf6287a6257cdebd5b8f12a7ebe364300ec157f34d.jpg)

接下来就是我们熟悉的部分的环节了。

![](images/314ee3effb9c6ac7a7bc73ceb26b3629653d863fb0fe488f34f65838ec2bc26d.jpg)

# 下载到网关，然后重启网关。

![](images/6116af3999e34bdc85d1307d99a3f07667fbb2755df3d2cc083af8c07ea15546.jpg)

# 7.4 扩展网关现有设备协议功能

我们在上面小节基础网关库设备基类的插件里，虽然可以扩展网关现有设备功能，但是存在一个升级兼容性的问题， 所以为了解决这个问题，我们推出了本小节的新的插件方式，在不依赖网关库的情况下，仍然可以扩展插件的一些功能(相比上面小节的扩展能力会弱一点)， 在本小节的方法给的例子里，主要是包含了两个代码文件 PluginsHelper.cs 和扩展设备类代码文件 DeviceVulcanizer.cs。

![](images/9d98874b35b1644286659327572651a9650bae26d8323d6d1379e091b20d5b3a.jpg)

这时候我们可以内置初始化请求，内置请求后，网关重启就可以看到数据信息：

# 动态设置点位数据

如果插件设备想要使用代码主动设置当前的某个点位也是支持的，我们内置一个标量缓存请求，就是相当于一个内存变量，内置请求如下所示

![](images/8387d76761f655a8d5afdb31b6b46abc0c3b7b1aec41d12e66e1ede281fec4cf.jpg)

# 更多的内容参考插件的在线开发文档

# 7.5 插件的安装和卸载

当我们开发好一个插件的时候，需要安装到边缘网关服务器，我们可以直接手动安装，在网关服务器路径的插件目录下，创建插件名称的文件夹，然后插件的文件放进去。例如下面：

![](images/f91c709f3d0c2ec6489981df1e921ef1d016c4788299425832bdd4438afb2fc9.jpg)

如果是卸载插件的话，我们就直接删除 hugong.delixi 这个目录即可。然后重启网关服务器生效。如果这时候我们从客户端来进行远程的插件安装和卸载的话，需要这么操作。

![](images/b7be0ff56ee547e4bbf53e6440556032e2855e14e6d0da2c58928e8862bacf22.jpg)

显示如下的界面，我们可以查看，安装，更新，删除插件信息。

![](images/9c00798fa2320170408cc6a1f7c0127ce4c687f1078a0db197a40236b3284468.jpg)

我们现在来安装一个插件试试看，点击右上侧的从本地安装/更新插件按钮，选择想要安装的插件。

![](images/7a868d498bfc9adb7dbdfe76aa952279310b8e8d5fc7d65847eac3f015c38b2a.jpg)  
注意：需要根据服务器的运行环境来安装指定环境的插件，通常是.NetFramework4.6.1及以上或是.netstandard2.

提示上传成功，并且成功加载，表示一切正常，新安装插件的时候，立即生效，可以直接添加插件设备了，如果网关已经存在该插件了，则会提示覆盖确认：

![](images/88ae81463a33bde8b8a61bf31bd7c6f0d7480957190d26f1f845af58d14def45.jpg)

更新插件以及卸载插件是需要重启网关的服务器才能生效的。安装完成之后的效果如下：

![](images/5e19c066eb8ada1d6857cb6305a2cfea5760cbb2c339344f0ac8b54248e7a7f2.jpg)

当前的操作同样适用于linux 版本的边缘网关服务器。

# 八、使用 Node-Red 扩展

# 8.1 Node-Red 基本说明和使用

什么是 Node-Red？这是一个可以拖拽的流程化脚本工具，官网为 https://nodered.org/，使用本工具可以大幅提升边缘网关系统的灵活性，实现一些特殊需求的功能。

我们以Windows 系统为例子，实际这个工具也支持 Linux 系统的。我们在 Windows 系统上运行边缘网关系统，然后开始安装Node-Red，官网有详细的教程。

https://nodered.org/docs/getting-started/windows

基本的流程时安装 Node.js，再使用 npm install -g --unsafe-perm node-red 命令安装Node-Red。最后使用 node-red 命令来运行。

命令提示符  
Microsoft Windows [版本 10.0.22000.556](c）Microsoft Corporation。保留所有权利。C:\Users\<USER>\0D[调试] 2022-08-18 16:19:03.379 Thread [027] Serial->Mqtt : A0001222\0D[调试] 2022-08-18 16:19:03.831 Thread [027] Serial->Mqtt: A0001222\0D[调试] 2022-08-18 16:19:04.038 Thread [004] Serial->Mqtt: A0001222\0D[调试] 2022-08-18 16:19:04.256 Thread [027] Serial->Mqtt : A0001222\0D[调试] 2022-08-18 16:19:04.510 Thread [029] Serial->Mqt : A0001222\0D[调试]2022-08-18 16:19:04.779 Thread [006] Serial->Mqtt: A0001222\0D[调试] 2022-08-18 16:19:04.996 Thread [029] Serial->Mqtt : A0001222\0D[调试] 2022-08-18 16:19:05.222 Thread [004] Serial->Mqtt: A0001222\0D服务器列表 中 设备监视界面×+ 设备名称：Edge:DD57F968D9FE 开始运行时间：2022-08-18 16:18:34 Version: 1.4.0 66 05日081Edge:DD57F968D9FE [127.0.0.1] 设备6报警2ad ModbusTcp 设备名称：ModbusTcp 设备名称：Modbus 服务器 设备名称：西门子设备MsModbus服务器 活动时间：2022-08-18 16:23:43 活动时间：2022-08-18 16:23:43 活动时间：2022-08-18 16:23:43SIE西门子设备 采集耗时：105 ms 采集耗：0ms 采集耗时：210 msTcpToTcp 成功次数：927 失败次数：0 成功次数：617 失败次数：0 成功次数：1854 失败次数：0Y9 SerialToMqtt 0。 ▲。 ×。 xo 0。 ×。 ×o 2 ▲0 ×。 XoD-CFE. ：o181612342 ：28181619:05 设：22LC-MC-34集：0m 失败次数：0 采集时：0ms 失败次数：0 4.6分钟 采集时：0ms 失败次数：0 5.2分钟Edge:431572DD8C19 [127.0.0.1] ▲o ×。 Xo 0。 ▲。 ×。 xo △0 ×。 Xo数据信息设备信息方法接口报警信息OEE信息离线信息设备日志节点名称：SerialToMqtt 1这个设备的节点名字就是MQTT的topic值数据名称 值(Value) 类型 权限 描述coM [COM6] Open String Read cOM口的状态信息Serial- >Mqtt 81 Int64 Read 串口到MQTT的报文字节...Mqtt- >Serial 0 Int64 Read MQTT到串口的报文字节..Topic SerialToMqtt String Read MQTT订阅的主题，就可.2也可以参考这个topic值信息HslCommunication 测试工具 □所bu言报文日志首网API文档 Verin 全国使用分布 Ram: 35.9MB Timeout:0 Lock:0 Wait:0  
NEMelsec Plc [三菱] 博客地址： https://www.cnblogs.com/dathlin/p/11631894.html 使用协议： MQTT 3端口号注意，还有用户名密码 保存连接-SIE Siemens Plc [西门子]日志监 Movbns Ple Ip地址：127.0.0.1 端口号：521 接收超时：5000 KeepLive: 100 □ RSA加密 (需要服务器支持) 断开连接  
设备 -om Omron Plc[欧姆龙] 客户端标识： 用户名: admin 密码: 123456 遗嘱-5 LSis Ple Pl[基恩] Subscribed: (已订阅) Topic: SerialToMqtt 4输入正确的topic 主题信息 订阅 取消订阅里Pan Panasonic Plc[松下] Payload:

然后我们点击订阅数据，就可以接受到真实的数据信息了。

![](images/d31e1bf16b4c4695ed080ea518d6d2e3933d84d7dfb460160bbab58560a5ad54.jpg)

如果需要回写数据给设备，就可以发布数据，例如：

![](images/fe2c14d2b227ff953f69eb2e261d9bcf1d16dcfd2ac301f06c28bebb270025bb.jpg)

然后去设备上校验是否真的收到数据了。

# 9.4 自由的网口及串口通信

本小节介绍当碰到一些自定义的协议和网关系统未曾实现的协议，如何来配置实现数据解析的操作。我们假设ModbusTcp 协议没有在网关里实现，现在需要读取寄存器地址 100 的数据，然后解析出实际的 short 类型数据。那么从通信的报文上来理解，就是如下的收发报文（以下是一个例子）：

# 发送：00 00 00 00 00 06 01 03 00 64 00 01 接收：00 00 00 00 00 05 01 03 02 00 7B

现在就是在网关里实现上面的报文收发，并且获取到最终的数据 00 7B，解析出实际的数据：123我们先添加一个自由以太网的设备信息：

![](images/bc2decee5a4d48aa53963c77e539005939ca2418b9f9416882bd342be2d35b91.jpg)

这里的信息配置很好理解，就是常规的信息配置，设备的名称，设备的 IP 地址及端口号信息，如果是自由的串口设备的话，就是串口的基本参数信息，包括串口名，波特率，数据位，停止位，检验位等等信息，我们来看一些高级的配置，我们从实际分析入手，这时候碰到第一个问题：

# 问题一：网关怎么知道数据是否接收完整？

默认情况下，如果不指定的话，网关的 TCP 就是接收一次缓存数据，不管长度多少，反正接收到数据就算（串口也是类似的），可能在大多数时候都是正常工作的，但是难免有时候设备的问题，或是网络问题，导致数据分批过来了，这时候就接收不到完整的数据了，这时候就需要配置消息完整性的检查操作。经过大量的经验得出，绝大部分的协议满足以下3 种情况的长度检查：

1. 固定长度，例如每次交互都是 20 个字节，固定的  
2. 固定长度+可变长度，固定长度的头子节可以计算出剩余可变长度  
3. 固定结尾字符，大多数的串口协议都是以0d 0a 结尾的

# 情况一：固定长度

我们先来看第一种情况的配置，就是 20 个字节的情况：

![](images/68469169bb4c898724da79cfd454ec1167bc34e1b52e218724076af633d0c68b.jpg)

这样就配置好了。

# 情况二： 固定长度+可变长度

以上面的Modbus 为例子，根据Modbus 协议规约，前6 个字节是固定长度，后面的长度根据第5，6 个字节计算出来的，是一个short 类型的数据，那么可以配置如下：

![](images/b942509efa5da67283bb85970a42cfcfc721965b867ea527afee7eed2e482bfa.jpg)

# 情况三： 固定字符结尾

假如你的报文是\r\n 结尾的，那么就是

正口用C I aise  
字节转换规则 LittleEndianOrder  
消息结构类型 SpecifiesEndChar 消息结构类型为指定字符结尾  
固定长度信息 6 1 结尾字符为0D0A  
指定结尾字符 OD OA 如果只有 个字符结尾， 直接写0D  
解析的消息规则信息 RegularScalarNode[_MessageRegularshort:5]

好了，目前按照情况二配置了消息检查的机制。

# 问题二：网关怎么去解析原始报文？大端？小端？

在经过大量的经验得出，绝大部分的协议满足以下 3 种情况的数据解析

1. 大端，例如西门子 PLC  
2. 小端，例如三菱的MC 协议  
3. 特殊的，例如 Modbus 协议，字反转+CDAB

此处可以配置第三种情况，

<html><body><table><tr><td colspan="2">回驭配且后息</td></tr><tr><td>是否异步推送</td><td>False</td></tr><tr><td>是否使用长连接</td><td>True</td></tr><tr><td>是否使用ASCII格式</td><td>False</td></tr><tr><td>字节转换规则</td><td>LittleEndianOrder 有三种情况可以使用</td></tr><tr><td>消息结构类型</td><td>BigEndianOrder</td></tr><tr><td>固定长度信息</td><td>LittleEndianOrder</td></tr><tr><td>指定结尾字符</td><td>ReverseByWord</td></tr><tr><td>解析的消息规则信息</td><td>RedularScalarNodelMessadeRedular:short:4l</td></tr></table></body></html>

这里我们选择第三种 ReverByWord

# ModbusTcp 为例子

好了，我们已经成功添加了一个设备，然后要增加一个原始字节请求的操作。

![](images/c0d0ce7c5b20f9168be0a50ee540877039a9f85f2dd79ce406dec160901cafa8.jpg)

# 然后我们再增加一个标量的解析，由报文得知是字节索引 9 的 short 数据。

![](images/cdac59858e3e9785876a07684fbaf6ce1ee358c279d08fc77fd6251fa2f7d79e.jpg)

# 保存，下载到网关，重启查看数据：

边缘网关系统客户端

![](images/2d1741a6d2ffa54443d0a22234984e103a49156cee2970feb14962cd7f2612c0.jpg)

# 看下设备日志：

数据信息设备信息方法接口报警信息OEE信息离线信息 设备日志   
设备url:TcpFreedom   
日志列表： ○运行日志 通信报文 暂停 清空   
[调试] 2023-02-28 23:46:25.018 Thread [015]TcpFreedom[127.0.0.1:502]:接收:00 00 00 00 00 05 01 03 02 00 7B   
[调试]2023-02-28 23:46:26.002 Thread [003]TcpFreedom[127.0.0.1:502]:发送:00 0000 00 0006 01 03 00 64 00 01   
[调试]2023-02-28 23:46:26.002 Thread [029]TcpFreedom[127.0.0.1:502]:接收:00 00 0000 0005 01 03 0200 7B   
[调试]2023-02-28 23:46:27.005 Thread [018]TcpFreedom[127.0.0.1:502]:发送:00 00 00 00 0006 01 03 00 64 00 01   
[调试]2023-02-28 23:46:27.005 Thread [029]TcpFreedom[127.0.0.1:502]:接收:00 00 00 00 0005 01 03 0200 7B   
[调试]2023-02-28 23:46:28.007Thread [021]TcpFreedom[127.0.0.1:502]:发送:000000000006010300 64 0001

所有的消息报文都是根据实际的情况解析的，当然一条报文可以解析出很多数据的，串口也是类似的。

# 基于串口的 ModbusAscii 例子

上面演示的是有个二进制通信的报文，如果是基于 ASCII 格式的串口通信又该如何配置？刚好有个ModbusAscii 协议可以作为经典的例子。例如我们通常是读取地址 100 的数据，报文如下：

发送 :01030064000197\0D\0A接收 :010302007B7F\0D\0A

此处的报文按照ASCII 格式显示，遇到不可见字符使用转义表示，这时候我们配置的自由串口参数如下：

![](images/6817432685bf307b3c8e71b21eb8a576419c69864873db7b7b1ce2a9509c2217.jpg)

然后进入高级配置的，主要配置消息完整性检查，以及数据规则解析。

高级配置信息是否异步推送 False是否使用ASCII格式 True 使用ASCI格式通信字节转换规则 ReverseByWord 字节转换规则设置消息结构类型 SpecifiesEndChar 消息结构设置指定字符结尾固定长度信息 0指定结尾字符 OD OA指定结尾字符0D0A解析的消息规则信息 RegularScalarNode[_MessageRegular:short是否使用ASCII格式获取或设置是否使用ASCII的格式字符串来解析地址和记录电文通信，虽然是ASCII报文，仍然支持输入测试通信\OD\OA 来表示\r\n等不可见字符

好了，这个设备的基本配置已经完成了，现在来添加原始字节的请求操作，参考下面的截图内容，在地址里输入 ASCII 格式的报文即可。

![](images/0c1fbf5fa654d9f6f7c47cda220011751e1af244edf5e0aaa6e7dcf36c935d51.jpg)

然后我们开始配置解析数据，再回顾下刚才接收的报文信息，

:010302007B7F\0D\0A

这是一个ASCII 格式的数据，007B 是我们需要的数据。可以数一下，起始索引 7，那么配置如下：

![](images/89ef2c064267cb2688f5782eec71355fbaa20893f6f68ca1e9691f460cc164e6.jpg)

然后把配置信息写入到网关设备中去，然后重启，可以看到采集到了正确的数据信息。

![](images/1e43735fcd87cebc626d43c26810f1888ccd6b193d76cb342b4636a3470675d1.jpg)

然后我们再去配置界面看下配置的解析，右上角有个数据预览的选择，我们可以点击看看（这个功能需要先配置设备节点，原始字节请求，下载到网关重启才能生效）

![](images/2a3e12984fddf613445eba5ab11408d3f8e48d82db49e685a8bff44da129d000.jpg)

我们按照ASCII 格式显示，这时候可以看到实际的数据信息。

# 9.5 设备分身(富设备使用)

本小节介绍另一种可能的实际情况，在现场很多小的子设备的数据通过一个总的 PLC 采集的，所以这里的情况相当于，有一个独立的设备，但是这个设备的数据在另一个 PLC 上面，我们在网关中是需要配置一个富设备，关联下实际的 PLC 即可。如下所示：

![](images/3d19337f59b362194c34ca28551f28af741f0ae8b0042e7208246ec2295c45d5.jpg)

这时候我们有一个标准的 PLC 设备，里面有两个数据，这时候，我们还有一个设备是电机，但是数据在 PLC 里面的，这时候我们就可以新增一个富设备，并且关联这个三菱PLC 设备了，在此之前，我们需要查看下这个三菱PLC 的唯一路径是什么？

中 设备监视界面 采集信息-本地测试当前选择设备路径： Edge:A8907FF0BD68 /工厂-/三菱PLC ② 此处复制文本框即可 设备数量：1|50系统的树节点信息：(右键操作，支持拖拽，复制粘贴) 数据列表 /工厂-/三菱PLCDevices工厂- <： 节点信息三菱PLC 选中这个设备 节点名称 三菱PLCDO 节点别名节点的描述信息 此设备安装在角落，编号0001且Regular 节点类型Alarm √ 设备信息OEE 设备类型Template 设备使用状态 OnWorkDatabase 创建日期安装日期 2023年11月28日

我们复制上图的文本框的数据，这样我们就可以获取到三菱PLC 的唯一路径了，这时候我们新增一个富设备，属性里设置这个唯一路径。

设备监视界面 采集信息-本地测试  
幽 当前选择设备路径： Edge:A8907FF0BD68 /工厂-  
系统的树节点信息：(右键操作，支持拖拽，复制粘贴) 数据列表且Devices白 0 右键菜单 节点信息三菱PLC 新增分类节点名称D0 新增管道 节点别名D2 + 新增Robot 节点的描述信息Regular + 新增PLC等设备 1 电 富设备(Machine) 2 富设备且Alarm + 新增Cnc 1 SIE 西门子Plc(Siemens)OEE 新增特殊设备 1 六 三菱PLC(Melsec) 1且Template 四 新增插件设备 om 欧姆龙Plc(Omron) 1

![](images/18ca0fc71bdd0e3ee902a5dc678d630f8528ed2ff1308ddc8e95b8f848c81c77.jpg)

这样我们就添加了一个富设备信息了，然后我们再配置请求，新增一个标量请求，地址是 D100 的short 值，再添加一个缩小 10 倍的 D102 的点位看看。

![](images/440a03b7cb2b354c5b3554fed026ad5f818f02c7b2899d92c3840430b3804c3b.jpg)

然后下载到 PLC，断电重启操作。就可以看到当前的设备情况了，即使是写入也是支持的。

![](images/8206ea6cdb95fb1aa80bf837be84441ce0f6a7a2cfe3b51d4aefb3eaef0b3ee1.jpg)

# 哪怕我们配置了上传 MQTT 服务器，此处传本地的测试服务器。

![](images/646742424c819283ffcad28ef968e2e6763a18bd3707e426335f99ce758c24a0.jpg)

然后保存到网关，上传部分的逻辑不需要重启就可以看到效果。

选择 D:\TestSoftware\EdgeViewer\MqttServerSample.exe □ × 息的客 端id:Edge:A8907FF0BD68 时 1-28 14:10:49.000 俏息的主是 :"Edge:A8907FF0BD68"，" version":"1.7.2"," startie:"20-1-813:482, standbyStatus";""" "actieie":"2023--2814:10:48”"devicenieCot":2,"i timeDeviation:0. 杭州胡工物联科技有公司 deviceStatus" BD68 1-2814:10:49.000 的主题 工 14:10:47 captureSpendTime":0, _deviceStatus":true requestEnable":true, _onlineTime" 2023-11-2813:48:24 _failedlsg" alarmCount":{"AlarmCount":O,“Hint":0,"Warn”:,"Er Fata1":0},D102":14.5,"D100":20 息的客 中端id:Edge:A8907FF0BD68 1-2814:10:49.000 有： unt":0,Hint":0,Warn：,rrr:0,Ftal":},“D:16,“D2:0.

# 然后是数据单标签变化上传的话，也简单的测试下，配置如下：

![](images/99372b5fb39c2c6d33df1c26a35cb0997a3db0bd62d8263c6981b76b96c875f8.jpg)

# 最终的效果如下：

![](images/cacbdc621a80d5a58b749e418dbc9cd651f9ba87363f6fb429c085a6a4a94eb8.jpg)

这时候如果配置了富设备的前置主题信息，也是完美支持的。富设备同样也是支持配置原始字节请求及解析操作的，也可以正确的反写。

![](images/03453b7d706f8d1abedb2edc7675f66e03ff5d170fa700ea2b53c77bdb739cf0.jpg)

图边缘网关系统客户端

本地测试 -C-  
服务器列表 口 设备监视界面× 采集信息-本地测试  
+c 设备名称：Edge:A8907FF0BD68 开始运行时间：2023-11-2815:32:02 Version: 1.7.2 3 03 e。 ×o  
□本地测试[127.0.0.1] 设备3 报警设备名称：三菱PLC 设备名称：电机 设备名称：风机三菱PLC 活动时间：2023-11-28 15:33:40 活动时间：2023-11-28 15:33:39 活动时间：2023-11-28 15:33:39DO 采集耗时：211 ms 采集耗时：0ms 采集耗时：0msD2 成功次数：194 失败次数：0 成功次数：288 失败次数：0 成功次数：96 失败次数：0中电机 ×。 ×o 0 ×。 ×0 0 8。 ×0D100D200 数据信息设备信息方法接口报警信息OEE信息离线信息设备日志D201 节点名称： 工厂-/电机白风机L[1 D300 数据名称 值(Value) Unit 类型 权限 描述D100 20 Int16 ReadWrite shortD102 16.8 Double ReadWrite short值D200 99 Int16 ReadWrite shortD201 Float ReadWrite float

# 十、最后总结

目前的版本实现了数据的可视化配置，采集，丰富的数据接口，支持方法的调用测试，功能上来说还是可以完善的更好，比如后期加入数据库支持，脚本支持，以及让边缘网关系统运行的更加的稳定，流畅。插件扩展功能为我司大力推广的功能，可以让自己的设备变成标准化的设备，比如目前大量的基于 Modbus 协议的仪器仪表，基于 PLC 的设备信息等等，可以给客户提供更加简单，纯净的接口，更加快速的集成，而不至于把时间都花费在数据点查找，编写上。

在 V1.0.1 版本里主要增加了 DTU 的模式，当边缘网关系统部署到云服务器的时候，还需要采集本地的串口或是网口设备就可以使用该模式，具体可以参考 3.15 小节的内容。

V1.1.0 版本主要增加了共享管道，以及设备模板的功能，在实际配置重复设备的时候，会方便很多，基于模板的插件也有更好的兼容性。

V1.2.0 版本增加了单线程管道的控制，以及数据库的支持，再配置 Node-Red 软件时可以展现更加强大的操作。

V1.3.0 版本增加了定时写入的数据请求功能，这个数据可以来自数值，数组，或是脚本，也可以来自网关的其他的数据节点，这样就可以构建出 Modbus 总站，或是三菱 PLC 总站，西门子 PLC 总站。

V1.4.0 版本优化了所有的原始字节请求的真实物理地址解析的功能，增加了对绝大部分的设备的通信报文的查看和监视操作，增加了串口转 MQTT，网口转 MQTT 的特殊设备。

V1.5.0 版本优化了一些已知的小问题，增加了数据标签的单位配置，原始字节请求关联其他设备的请求，增加了串口映射，增加了串口转远程MQTT，网口转远程 MQTT 功能。

V1.6.0 版本增加了基于网口和串口的自由通信协议的实现，可以实现一些特殊的，小众的设备通信实现，支持了使用MQTT，WebScoket 来发布数据然后写入设备的功能，报警也可直接写入数据库。

V1.7.0 版本主要优化了写入中文字符串正确反写设备，支持了字符串数组功能，结构体支持不展开，对象名属性名展开，属性名展开，定时写入的脚本改用表达式实现，支持了输入设备其他数据。

V1.7.2 版本主要优化DLT 设备字符串读写，网关接口支持数组读写，对外的MQTT 支持了反写操作，支持了对接 JetLinks 平台，优化了设备的线程调度执行插队任务，数据支持表达式变换。

V1.7.5 版本主要针对请求功能做了强化，支持了请求分类，可以同时做一些控制，以及请求功能增加了附加条件，实现了不定时，根据实际情况是否满足才执行的请求。

V1.8.0 版本主要针对网关的账户配置进行了增强，暂停继续请求支持控制分类，控制单个请求，插件功能大幅度优化，支持了完全自定义的通信协议实现，达到完全兼容升级的效果，推出在线文档。

尽管本软件在发布时已经经过了详细的测试，但还是无法保证所有的功能都没有任何的 bug，对于程序运行的异常，欢迎大家提供相关的异常记录，方便改进。当然如果您有更好的建议，可以联系QQ200962190, 1759967761 进行提交报告。

本手册如有表述不正确或是勘误，都可以发送到邮箱：<EMAIL>，感谢理解。