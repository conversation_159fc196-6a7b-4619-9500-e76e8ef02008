using FluentAvalonia.UI.Controls;
using IOTPlatform.SCADA.Services;
using IOTPlatform.Shared.Models;
using IOTPlatform.Shared.Services;

namespace IOTPlatform.SCADA;

public class SCADAModule : IModule
{
    private IRegionManager _regionManager;
    private IMenuService _menuService;
    
    public SCADAModule(IRegionManager regionManager, IMenuService menuService)
    {
        _regionManager = regionManager;
        _menuService = menuService;
        InitializeMenus();
    }
    
    private void InitializeMenus()
    {
        // 前台菜单
        var mainmenu = new WMenuItem
        {
            Title = "物联监控模块",
            Icon = Symbol.Audio,
            NavigationTarget = "DEMOView",
            MenuID = "12000",
            Index = 0
        };
        mainmenu.MenuItems.Add(new WMenuItem
        {
            Title = "工艺流程图", Icon = Symbol.Code, NavigationTarget = "DEMOView", MenuID = "12001", Index = 0
        });
        mainmenu.MenuItems.Add(new WMenuItem
        {
            Title = "数据看板", Icon = Symbol.Code, NavigationTarget = "DEMOView", MenuID = "12002", Index = 0
        });
        mainmenu.MenuItems.Add(new WMenuItem
        {
            Title = "远程控制", Icon = Symbol.Code, NavigationTarget = "DEMOView", MenuID = "12003", Index = 0
        });
        mainmenu.MenuItems.Add(new WMenuItem
        {
            Title = "报警处理", Icon = Symbol.Code, NavigationTarget = "DEMOView", MenuID = "12004", Index = 0
        });
        _menuService.AddMenuItem(mainmenu);
        // 后台菜单
        var setmenu = new WMenuItem
        {
            Title = "物联监控模块设置",
            Icon = Symbol.Setting,
            NavigationTarget = "",
            MenuID = "12100",
            Index = 0
        };
        setmenu.MenuItems.Add(new WMenuItem
        {
            Title = "边缘网关管理", Icon = Symbol.People, NavigationTarget = "DEMOView", MenuID = "12101", Index = 0
        });
        setmenu.MenuItems.Add(new WMenuItem
        {
            Title = "物联设备管理", Icon = Symbol.People, NavigationTarget = "DEMOView", MenuID = "12102", Index = 0
        });
        setmenu.MenuItems.Add(new WMenuItem
        {
            Title = "报警阈值管理", Icon = Symbol.ContactInfoFilled, NavigationTarget = "DEMOView", MenuID = "12103", Index = 0
        });
        setmenu.MenuItems.Add(new WMenuItem
        {
            Title = "报警联动管理", Icon = Symbol.ContactInfoFilled, NavigationTarget = "DEMOView", MenuID = "12104", Index = 0
        });
        _menuService.AddChildMenuItem(setmenu);
    }
    
    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        containerRegistry.RegisterSingleton<MrpcCommunicationService>();
        containerRegistry.RegisterSingleton<DataPollingService>();
        
        //containerRegistry.RegisterForNavigation<DEMOView, DEMOViewModel>();
    }

    public void OnInitialized(IContainerProvider containerProvider)
    {
        
    }
}