﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="ViewModels\" />
      <Folder Include="Views\" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="InfluxDB.Client" />
      <PackageReference Include="Newtonsoft.Json" />
      <PackageReference Include="Prism.Core" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\IOTPlatform.Shared\IOTPlatform.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="HslCommunication">
        <HintPath>hsllib\netstandard2.1\HslCommunication.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
