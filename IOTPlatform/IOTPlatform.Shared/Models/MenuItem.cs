using Avalonia.Controls.Templates;
using Avalonia.Metadata;
using FluentAvalonia.UI.Controls;

namespace IOTPlatform.Shared.Models;

public abstract class WMenuBase
{
    public required string MenuID { get; set; }
    public string? Title { get; set; }
}

public class WMenuItem() : WMenuBase, ICloneable
{
    public string? NavigationTarget { get; set; }
    public string? ToolTip { get; set; }
    public required Symbol Icon { get; set; }
    public required int Index { get; set; }
    public object? Params { get; set; }
    public List<WMenuBase> MenuItems { get; set; } = new List<WMenuBase>();
    public object Clone()
    {
        var clone = new WMenuItem
        {
            Title = this.Title,
            Icon = this.Icon,
            NavigationTarget = this.NavigationTarget,
            MenuID = this.MenuID,
            Index = this.Index
        };
        
        foreach (var item in MenuItems)
        {
            clone.MenuItems.Add(item);
        }
        
        return clone;
    }
}

public class Seperator : WMenuBase { }

public class Header : WMenuBase {
    public List<WMenuBase>? MenuItems { get; set; } = new List<WMenuBase>();
}

public class MenuItemTemplateSelector : DataTemplateSelector {
    [Content]
    public IDataTemplate? ItemTemplate { get; set; }
    public IDataTemplate? SeperatorTemplate { get; set; }
    public IDataTemplate? HeaderTemplate { get; set; }

    protected override IDataTemplate? SelectTemplateCore(object item) {
        return item is Seperator ? SeperatorTemplate
            : item is Header ? HeaderTemplate
            : ItemTemplate;
    }
}