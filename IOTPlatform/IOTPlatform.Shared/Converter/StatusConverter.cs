using System.Globalization;
using Avalonia.Data.Converters;

namespace IOTPlatform.Shared.Converter;

public class StatusConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is int status && status == 0)
        {
            return "启用";
        }
        if (value is int status1 && status1 == 1)
        {
            return "禁用";
        }
        return "未知";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}