using IOTPlatform.ORM.Entity;
using IOTPlatform.ORM.Repository;
using IOTPlatform.Shared.Models;

namespace IOTPlatform.Shared.Services;

public class UserService : IUserService
{
    private readonly UserRepository _userRepository;
    private readonly IMenuService _menuService;
    private SysUser? _currentUser;

    public UserService(UserRepository userRepository, IMenuService menuService)
    {
        _userRepository = userRepository;
        _menuService = menuService;
    }

    public async Task<LoginResult> LoginAsync(string username, string password)
    {
        var user = await _userRepository.GetUserByUsernameAsync(username);
        if (user == null)
        {
            return new LoginResult { Success = false, Message = "用户名或密码错误" };
        }
        if (user.password != password) // 实际应用中应该使用加密后的密码比较
        {
            return new LoginResult { Success = false, Message = "用户名或密码错误" };
        }
        if (user.status != 0)
        {
            return new LoginResult { Success = false, Message = "用户已被禁用" };
        }
        _currentUser = user;
        return new LoginResult { Success = true };
    }

    public async Task<SysUser?> GetCurrentUserAsync()
    {
        return await Task.FromResult(_currentUser);
    }

    public async Task<List<string>> GetUserMenusAsync()
    {
        if (_currentUser == null)
            return new List<string>();

        return await _userRepository.GetUserMenusAsync(_currentUser.user_id);
    }
    
    public async Task<IEnumerable<WMenuBase>> GetUserFrontMenusAsync(long userId)
    {
        try
        {
            var _fullmenu = _menuService.GetMenuItems();
            var menuIds = await _userRepository.GetUserMenusAsync(userId);
            // 根据用户权限过滤菜单
            var result = new List<WMenuBase>();
        
            foreach (var menu in _fullmenu)
            {
                if (menuIds.Contains(menu.MenuID))
                {
                    result.Add(menu);
                }
                else
                {
                    if (menu is WMenuItem menuItem && menuItem.MenuItems.Any())
                    {
                        // 处理子菜单
                        var filteredChildren = menuItem.MenuItems
                            .Where(child => menuIds.Contains(child.MenuID))
                            .ToList();

                        if (filteredChildren.Any())
                        {
                            var newMenuItem = menuItem.Clone() as WMenuItem;
                            if (newMenuItem != null)
                            {
                                newMenuItem.MenuItems.Clear();
                                newMenuItem.MenuItems.AddRange(filteredChildren);
                                result.Add(newMenuItem);
                            }
                        }
                    }
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            // 记录错误并返回空菜单列表
            System.Diagnostics.Debug.WriteLine($"获取用户菜单失败: {ex.Message}");
            return new List<WMenuBase>();
        }
    }
    
    public async Task<IEnumerable<WMenuBase>> GetUserBackMenusAsync(long userId)
    {
        try
        {
            var _fullmenu = _menuService.GetChildMenuItems();
            var menuIds = await _userRepository.GetUserMenusAsync(userId);
            // 根据用户权限过滤菜单
            var result = new List<WMenuBase>();
        
            foreach (var menu in _fullmenu)
            {
                if (menuIds.Contains(menu.MenuID))
                {
                    result.Add(menu);
                }
                else
                {
                    if (menu is WMenuItem menuItem && menuItem.MenuItems.Any())
                    {
                        // 处理子菜单
                        var filteredChildren = menuItem.MenuItems
                            .Where(child => menuIds.Contains(child.MenuID))
                            .ToList();

                        if (filteredChildren.Any())
                        {
                            var newMenuItem = menuItem.Clone() as WMenuItem;
                            if (newMenuItem != null)
                            {
                                newMenuItem.MenuItems.Clear();
                                newMenuItem.MenuItems.AddRange(filteredChildren);
                                result.Add(newMenuItem);
                            }
                        }
                    }
                    else
                    {
                        result.Add(menu);
                    }
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            // 记录错误并返回空菜单列表
            System.Diagnostics.Debug.WriteLine($"获取用户菜单失败: {ex.Message}");
            return new List<WMenuBase>();
        }
    }

    public async Task<IEnumerable<WMenuBase>> GetUserAllFrontMenusAsync()
    {
        var _fullmenu = _menuService.GetChildMenuItems();
        return await Task.FromResult<IEnumerable<WMenuBase>>(_fullmenu.ToList());
    }

    public async Task<IEnumerable<WMenuBase>> GetUserAllBackMenusAsync()
    {
        var _fullmenu = _menuService.GetChildMenuItems();
        var result = new List<WMenuBase>();
        result.AddRange(_fullmenu);
        return await Task.FromResult<IEnumerable<WMenuBase>>(result);
    }

    public void Logout()
    {
        _currentUser = null;
    }
}