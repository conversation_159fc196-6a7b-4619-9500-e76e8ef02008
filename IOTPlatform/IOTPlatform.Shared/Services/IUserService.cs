using IOTPlatform.ORM.Entity;
using IOTPlatform.Shared.Models;

namespace IOTPlatform.Shared.Services;

public interface IUserService
{
    Task<LoginResult> LoginAsync(string username, string password);
    Task<SysUser?> GetCurrentUserAsync();
    Task<List<string>> GetUserMenusAsync();
    Task<IEnumerable<WMenuBase>> GetUserFrontMenusAsync(long userId);
    Task<IEnumerable<WMenuBase>> GetUserBackMenusAsync(long userId);
    Task<IEnumerable<WMenuBase>> GetUserAllFrontMenusAsync();
    Task<IEnumerable<WMenuBase>> GetUserAllBackMenusAsync();
    void Logout();
}

public class LoginResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}