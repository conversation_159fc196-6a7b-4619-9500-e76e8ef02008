using System.Collections.Generic;
using System.Collections.ObjectModel;
using FluentAvalonia.UI.Controls;
using IOTPlatform.Shared.Models;

namespace IOTPlatform.Shared.Services;

public class MenuService : IMenuService
{
    private List<WMenuBase> _fullmenu = new List<WMenuBase>();
    private List<WMenuBase> _childmenu = new List<WMenuBase>();
    private ObservableCollection<WMenuBase> _menuItems = new ObservableCollection<WMenuBase>();
    private ObservableCollection<WMenuBase> _childmenuItems = new ObservableCollection<WMenuBase>();
    
    public MenuService()
    {
        InitializeMenus();
        
    }

    private void InitializeMenus()
    {
        // 前台菜单
        var homemenu = new WMenuItem
        {
            Title = "首页",
            Icon = Symbol.Setting,
            NavigationTarget = "WellcomeView",
            MenuID = "10000",
            Index = 0
        };
        _fullmenu.Add(homemenu);
        // 后台菜单
        var setmenu = new WMenuItem
        {
            Title = "系统设置",
            Icon = Symbol.Setting,
            NavigationTarget = "",
            MenuID = "11000",
            Index = 0
        };
        setmenu.MenuItems.Add(new WMenuItem
        {
            Title = "主题设置", Icon = Symbol.DarkTheme, NavigationTarget = "AppThemeView", MenuID = "11003", Index = 0
        });
        setmenu.MenuItems.Add(new WMenuItem
        {
            Title = "用户设置", Icon = Symbol.People, NavigationTarget = "UsersView", MenuID = "11001", Index = 0
        });
        setmenu.MenuItems.Add(new WMenuItem
        {
            Title = "角色设置", Icon = Symbol.ContactInfoFilled, NavigationTarget = "RolesView", MenuID = "11002", Index = 0
        });
        _childmenu.Add(setmenu);
        foreach (var item in _fullmenu) _menuItems.Add(item);
        foreach (var item in _childmenu) _childmenuItems.Add(item);
        //_menuItems.Add(new Header { Title = "AAA" });
        //_menuItems.Add(new Seperator());
    }

    public IEnumerable<WMenuBase> GetMenuItems()
    {
        return _menuItems;
    }

    public IEnumerable<WMenuBase> GetChildMenuItems()
    {
        return _childmenuItems;
    }

    public bool AddMenuItem(WMenuBase menuItem)
    {
        if (_fullmenu.Exists(m=>m.MenuID == menuItem.MenuID))
        {
            return false;
        }
        else
        {
            _fullmenu.Add(menuItem);
            _menuItems.Add(menuItem);
            return true;
        }
    }

    public bool AddChildMenuItem(WMenuBase menuItem)
    {
        if (_childmenu.Exists(m=>m.MenuID == menuItem.MenuID))
        {
            return false;
        }
        else
        {
            _childmenu.Add(menuItem);
            _childmenuItems.Add(menuItem);
            return true;
        }
    }
}

