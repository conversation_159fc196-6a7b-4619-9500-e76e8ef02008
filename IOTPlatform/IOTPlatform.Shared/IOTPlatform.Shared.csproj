﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Avalonia" />
      <PackageReference Include="CommunityToolkit.Mvvm" />
      <PackageReference Include="FluentAvaloniaUI" />
      <PackageReference Include="Prism.Avalonia" />
      <PackageReference Include="Prism.Core" />
      <PackageReference Include="Serilog" />
      <PackageReference Include="Serilog.Sinks.Console" />
      <PackageReference Include="Serilog.Sinks.File" />
      <PackageReference Include="System.Collections" />
      <PackageReference Include="System.ObjectModel" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\IOTPlatform.ORM\IOTPlatform.ORM.csproj" />
    </ItemGroup>

</Project>
