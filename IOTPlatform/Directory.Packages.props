<Project>
  <!-- https://learn.microsoft.com/en-us/nuget/consume-packages/central-package-management -->
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <!-- Avalonia packages -->
    <!-- Important: keep version in sync! -->
    <PackageVersion Include="Avalonia" Version="11.3.2" />
    <PackageVersion Include="Avalonia.Themes.Fluent" Version="11.3.2" />
    <PackageVersion Include="Avalonia.Fonts.Inter" Version="11.3.2" />
    <PackageVersion Include="Avalonia.Diagnostics" Version="11.3.2" />
    <PackageVersion Include="Avalonia.Desktop" Version="11.3.2" />
    <PackageVersion Include="Avalonia.iOS" Version="11.3.2" />
    <PackageVersion Include="Avalonia.Browser" Version="11.3.2" />
    <PackageVersion Include="Avalonia.Android" Version="11.3.2" />
    <PackageVersion Include="Avalonia.Xaml.Behaviors" Version="11.3.0.6" />
    <PackageVersion Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageVersion Include="FluentAvaloniaUI" Version="2.4.0-preview1" />
    <PackageVersion Include="FluentValidation" Version="12.0.0" />
    <PackageVersion Include="InfluxDB.Client" Version="4.19.0-dev.15190" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="10.0.0-preview.5.25277.114" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="10.0.0-preview.5.25277.114" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.FileExtensions" Version="10.0.0-preview.5.25277.114" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="10.0.0-preview.5.25277.114" />
    <PackageVersion Include="MySql.Data" Version="9.3.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Prism.Avalonia" Version="9.0.537.11300-pre" />
    <PackageVersion Include="Prism.Core" Version="9.0.537" />
    <PackageVersion Include="Prism.DryIoc.Avalonia" Version="9.0.537.11300-pre" />
    <PackageVersion Include="Projektanker.Icons.Avalonia" Version="9.6.2" />
    <PackageVersion Include="Projektanker.Icons.Avalonia.FontAwesome" Version="9.6.2" />
    <PackageVersion Include="Projektanker.Icons.Avalonia.MaterialDesign" Version="9.6.2" />
    <PackageVersion Include="Serilog" Version="4.3.1-dev-02373" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.1-dev-00953" />
    <PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageVersion Include="SqlSugarCore" Version="5.1.4.197-preview14" />
    <PackageVersion Include="System.Collections" Version="4.3.0" />
    <PackageVersion Include="System.ObjectModel" Version="4.3.0" />
    <PackageVersion Include="Xamarin.AndroidX.Core.SplashScreen" Version="1.0.1.16" />
  </ItemGroup>
</Project>