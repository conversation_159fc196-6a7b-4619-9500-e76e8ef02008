﻿<Project Sdk="Microsoft.NET.Sdk.WebAssembly">
    <PropertyGroup>
        <TargetFramework>net9.0-browser</TargetFramework>
        <OutputType>Exe</OutputType>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia.Browser"/>
        <PackageReference Include="Prism.DryIoc.Avalonia" />
        <PackageReference Include="Serilog.Sinks.File" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\IOTPlatform\IOTPlatform.csproj"/>
    </ItemGroup>
</Project>
