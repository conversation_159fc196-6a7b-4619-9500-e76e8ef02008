using IOTPlatform.ORM.Entity;

namespace IOTPlatform.ORM.Repository;

public class UserRepository : BaseRepository<SysUser>
{
    public UserRepository(DbContext.DbContext context) : base(context) { }
    
    public async Task<List<SysRole>> GetRolesAsync()
    {
        // 获取所有有效的角色
        return await Context.Db.Queryable<SysRole>()
            .Where(r => r.status == 0)  // 假设 status = 0 表示有效角色
            .OrderBy(r => r.role_id)
            .ToListAsync();
    }

    public async Task<long> InsertUserWithRolesAsync(SysUser user, List<long> roleIds)
    {
        // 检查用户名是否已存在
        var exists = await Context.Db.Queryable<SysUser>()
            .AnyAsync(x => x.username == user.username);

        if (exists)
        {
            return 0; // 用户名已存在，返回0表示插入失败
        }
        try 
        {
            await Context.Db.UseTranAsync(async () =>
            {
                // 插入用户
                var rows = await Context.Db.Insertable(user).ExecuteReturnIdentityAsync();
                user.user_id = rows;  // 设置新插入用户的ID

                if (roleIds.Any())
                {
                    // 插入用户角色关系
                    var userRoles = roleIds.Select(roleId => new SysUserRole
                    {
                        user_id = user.user_id,
                        role_id = roleId
                    }).ToList();

                    await Context.Db.Insertable(userRoles).ExecuteCommandAsync();
                }
                Context.Db.CommitTran();
            });
            return 1; // 事务成功
        }
        catch
        {
            Context.Db.RollbackTran();
            return 0; // 事务失败
        }
    }
    
    public async Task<List<long>> GetUserRolesAsync(long userId)
    {
        return await Context.Db.Queryable<SysUserRole>()
            .Where(ur => ur.user_id == userId)
            .Select(ur => ur.role_id)
            .ToListAsync();
    }

    public async Task<int> UpdateUserWithRolesAsync(SysUser user, List<long> roleIds)
    {
        try
        {
            var result = 0;
            Context.Db.BeginTran();

            // 更新用户信息
            result = await Context.Db.Updateable(user)
                .UpdateColumns(x => new
                {
                    x.nickname,
                    x.status,
                    x.update_time
                })
                .Where(x => x.user_id == user.user_id)
                .ExecuteCommandAsync();

            if (result > 0)
            {
                // 删除原有角色关系
                await Context.Db.Deleteable<SysUserRole>()
                    .Where(ur => ur.user_id == user.user_id)
                    .ExecuteCommandAsync();

                if (roleIds?.Any() == true)
                {
                    // 插入新的用户角色关系
                    var userRoles = roleIds.Select(roleId => new SysUserRole
                    {
                        user_id = user.user_id,
                        role_id = roleId
                    }).ToList();

                    await Context.Db.Insertable(userRoles).ExecuteCommandAsync();
                }
            }
            Context.Db.CommitTran();
            return result;
        }
        catch (Exception ex)
        {
            Context.Db.RollbackTran();
            Console.WriteLine($"UpdateUserWithRolesAsync error: {ex.Message}");
            return 0;
        }
    }
    
    public async Task<List<SysUser>> GetUsersWithRolesAsync()
    {
        return await Context.Db.Queryable<SysUser>()
            .Includes(u => u.UserRoles, ur => ur.Role)
            .OrderBy(u => u.user_id)
            .ToListAsync();
    }
    
    public async Task<bool> DeleteAsync(long userId)
    {
        try
        {
            // 使用事务包装删除操作
            await Context.Db.BeginTranAsync();
        
            // 先删除用户-角色关联
            await Context.Db.Deleteable<SysUserRole>()
                .Where(ur => ur.user_id == userId)
                .ExecuteCommandAsync();

            // 删除用户
            var result = await Context.Db.Deleteable<SysUser>()
                .Where(u => u.user_id == userId)
                .ExecuteCommandAsync();

            await Context.Db.CommitTranAsync();
            return result > 0;
        }
        catch (Exception)
        {
            await Context.Db.RollbackTranAsync();
            throw;
        }
    }
    
    public async Task<SysUser?> GetUserByUsernameAsync(string username)
    {
        return await Context.Db.Queryable<SysUser>()
            .Where(u => u.username == username)
            .Where(u => u.status == 0)
            .FirstAsync();
    }
    
    public async Task<List<string>> GetUserMenusAsync(long userId)
    {
        // 通过用户ID关联角色和菜单权限表获取菜单代码
        return await Context.Db.Queryable<SysUserRole>()
            .LeftJoin<SysRoleMenu>((ur, rm) => ur.role_id == rm.role_id)
            .Where((ur, rm) => ur.user_id == userId)
            .Select((ur, rm) => rm.menu_id.ToString())
            .Distinct()
            .ToListAsync();
    }
}