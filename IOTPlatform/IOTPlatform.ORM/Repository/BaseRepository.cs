namespace IOTPlatform.ORM.Repository;

public class BaseRepository<T> where T : class, new()
{
    protected DbContext.DbContext Context;
    
    public BaseRepository(DbContext.DbContext context)
    {
        Context = context;
        
    }

    public virtual async Task<bool> Add(T entity)
    {
        return await Context.Db.Insertable<T>(entity).ExecuteCommandAsync() > 0;
    }

    public virtual async Task<bool> Delete(long id)
    {
        return await Context.Db.Deleteable<T>().In(id).ExecuteCommandAsync() > 0;
    }

    public virtual async Task<bool> Update(T entity)
    {
        return await Context.Db.Updateable(entity).ExecuteCommandAsync() > 0;
    }

    public virtual async Task<T> GetById(long id)
    {
        return await Context.Db.Queryable<T>().InSingleAsync(id);
    }
    
    public virtual async Task<List<T>> SelectAllAsync()
    {
        return await Context.Db.Queryable<T>().ToListAsync();
    }
}