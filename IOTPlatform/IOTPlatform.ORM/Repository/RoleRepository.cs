using IOTPlatform.ORM.Entity;
using SqlSugar;

namespace IOTPlatform.ORM.Repository;

public class RoleRepository : BaseRepository<SysRole>
{
    public RoleRepository(DbContext.DbContext context) : base(context) { }
    
    public async Task<List<SysRole>> GetRolesAsync()
    {
        return await Context.Db.Queryable<SysRole>()
            .OrderBy(r => r.create_time, OrderByType.Desc)
            .ToListAsync();
    }

    public async Task<bool> CreateAsync(SysRole role)
    {
        return await Context.Db.Insertable(role).ExecuteCommandAsync() > 0;
    }

    public async Task<bool> UpdateAsync(SysRole role)
    {
        return await Context.Db.Updateable(role)
            .IgnoreColumns(r => new { r.create_time })
            .ExecuteCommandAsync() > 0;
    }

    public async Task<bool> DeleteAsync(long roleId)
    {
        try
        {
            // 开启事务
            await Context.Db.BeginTranAsync();

            // 先删除角色-用户关联
            await Context.Db.Deleteable<SysUserRole>()
                .Where(ur => ur.role_id == roleId)
                .ExecuteCommandAsync();
            
            // 删除角色-菜单关联
            await Context.Db.Deleteable<SysRoleMenu>()
                .Where(rm => rm.role_id == roleId)
                .ExecuteCommandAsync();

            // 删除角色
            var result = await Context.Db.Deleteable<SysRole>()
                .Where(r => r.role_id == roleId)
                .ExecuteCommandAsync() > 0;

            // 提交事务
            await Context.Db.CommitTranAsync();
            return result;
        }
        catch (Exception)
        {
            // 回滚事务
            await Context.Db.RollbackTranAsync();
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string roleName, int? excludeId = null)
    {
        var query = Context.Db.Queryable<SysRole>()
            .Where(r => r.role_name == roleName);

        if (excludeId.HasValue)
        {
            query = query.Where(r => r.role_id != excludeId.Value);
        }

        return await query.AnyAsync();
    }
    
    public async Task<List<SysRoleMenu>> GetRoleMenusAsync(long roleId)
    {
        return await Context.Db.Queryable<SysRoleMenu>()
            .Where(rm => rm.role_id == roleId)
            .ToListAsync();
    }

    public async Task UpdateRoleMenusAsync(long roleId, List<string> menuIds)
    {
        using var tran = Context.Db.UseTran();
        try
        {
            // 删除原有权限
            await Context.Db.Deleteable<SysRoleMenu>()
                .Where(rm => rm.role_id == roleId)
                .ExecuteCommandAsync();

            // 添加新权限
            var roleMenus = menuIds.Select((string menuId) => new SysRoleMenu
            {
                role_id = roleId,
                menu_id = Convert.ToInt32(menuId)
            }).ToList();

            await Context.Db.Insertable(roleMenus).ExecuteCommandAsync();
            tran.CommitTran();
        }
        catch
        {
            tran.RollbackTran();
            throw;
        }
    }
}