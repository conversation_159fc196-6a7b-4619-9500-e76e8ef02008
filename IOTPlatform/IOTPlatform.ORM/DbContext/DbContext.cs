using IOTPlatform.ORM.Entity;
using SqlSugar;

namespace IOTPlatform.ORM.DbContext;

public class DbContext
{
    public SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
        {
            ConnectionString = "server=localhost;Database=IOTPlatformDB;Uid=root;Pwd=******",
            DbType = DbType.MySql,
            IsAutoCloseConnection = true
        },
        db =>
        {
            db.Aop.OnError = (exp) =>
            {
                Console.WriteLine(exp.Message);
            };
            db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(sql);
            };
        });

    public SimpleClient<SysUser> SysUserDb => new SimpleClient<SysUser>(Db);
    public SimpleClient<SysRole> SysRoleDb => new SimpleClient<SysRole>(Db);
    public SimpleClient<SysMenu> SysMenuDb => new SimpleClient<SysMenu>(Db);
    public SimpleClient<SysUserRole> SysUserRoleDb => new SimpleClient<SysUserRole>(Db);
    public SimpleClient<SysRoleMenu> SysRoleMenuDb => new SimpleClient<SysRoleMenu>(Db);
}