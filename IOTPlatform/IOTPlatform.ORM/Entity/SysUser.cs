using SqlSugar;

namespace IOTPlatform.ORM.Entity;

[<PERSON>T<PERSON>("sys_user")]
public class SysUser
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long user_id { get; set; }
    public string? username { get; set; }
    public string? password { get; set; }
    public string? nickname { get; set; }
    public int status { get; set; }
    public DateTime create_time { get; set; }
    public DateTime update_time { get; set; }
    
    [Navigate(NavigateType.OneToMany, nameof(SysUserRole.user_id))]
    public List<SysUserRole>? UserRoles { get; set; }
    
    [SugarColumn(IsIgnore = true)]
    public string RoleNames => UserRoles?
        .Where(ur => ur.Role != null && !string.IsNullOrEmpty(ur.Role.role_name))
        .Select(ur => ur.Role!.role_name)
        .DefaultIfEmpty(string.Empty)
        .Aggregate((a, b) => $"{a}, {b}") ?? string.Empty;
}