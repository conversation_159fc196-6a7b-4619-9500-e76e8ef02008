using SqlSugar;

namespace IOTPlatform.ORM.Entity;

[<PERSON>Table("sys_user_role")]
public class SysUserRole
{
    [SugarColumn(IsPrimaryKey = true)]
    public long user_id { get; set; }
    
    [SugarColumn(IsPrimaryKey = true)]
    public long role_id { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(user_id))]
    public SysUser? User { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(role_id))]
    public SysRole? Role { get; set; }
}