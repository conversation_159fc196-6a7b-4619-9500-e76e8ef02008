using SqlSugar;

namespace IOTPlatform.ORM.Entity;

public class SysMenu
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long MenuId { get; set; }
    public long ParentId { get; set; }
    public string? MenuName { get; set; }
    public string? MenuCode { get; set; }
    public string? Path { get; set; }
    public string? Icon { get; set; }
    public char MenuType { get; set; }
    public int SortOrder { get; set; }
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
}