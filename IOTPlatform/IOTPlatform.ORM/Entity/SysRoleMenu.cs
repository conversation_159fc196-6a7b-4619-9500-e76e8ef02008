using SqlSugar;

namespace IOTPlatform.ORM.Entity;

[SugarTable("sys_role_menu")]
public class SysRoleMenu
{
    [SugarColumn(IsPrimaryKey = true)]
    public long role_id { get; set; }
    
    [SugarColumn(IsPrimaryKey = true)]
    public long menu_id { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(role_id))]
    public SysRole? Role { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(menu_id))]
    public SysMenu? Menu { get; set; }
}