﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <!--If you are willing to use Windows/MacOS native APIs you will need to create 3 projects.
        One for Windows with net9.0-windows TFM, one for MacOS with net9.0-macos and one with net9.0 TFM for Linux.-->
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    </PropertyGroup>

    <PropertyGroup>
        <ApplicationManifest>app.manifest</ApplicationManifest>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia.Desktop"/>
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Include="Avalonia.Diagnostics">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Prism.DryIoc.Avalonia" />
        <PackageReference Include="Projektanker.Icons.Avalonia" />
        <PackageReference Include="Projektanker.Icons.Avalonia.FontAwesome" />
        <PackageReference Include="Projektanker.Icons.Avalonia.MaterialDesign" />
        <PackageReference Include="Serilog.Sinks.File" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\IOTPlatform\IOTPlatform.csproj"/>
    </ItemGroup>
</Project>
