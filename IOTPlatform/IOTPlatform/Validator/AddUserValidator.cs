using FluentValidation;
using IOTPlatform.ViewModels;

namespace IOTPlatform.Validator;

public class AddUserValidator: AbstractValidator<AddUserDialogViewModel>
{
    public AddUserValidator()
    {
        RuleFor(x => x.Username)
            .NotEmpty().WithMessage("用户名不能为空")
            .MinimumLength(3).WithMessage("用户名至少需要3个字符")
            .MaximumLength(20).WithMessage("用户名最多20个字符");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("密码不能为空")
            .MinimumLength(6).WithMessage("密码至少需要6个字符")
            .MaximumLength(20).WithMessage("密码最多20个字符");
    }
}