using FluentValidation;
using IOTPlatform.ViewModels;

namespace IOTPlatform.Validator;

public class AddRoleDialogValidator: AbstractValidator<AddRoleDialogViewModel>
{
    private readonly AddRoleDialogViewModel _viewModel;

    public AddRoleDialogValidator(AddRoleDialogViewModel viewModel)
    {
        _viewModel = viewModel;

        RuleFor(x => x.RoleName)
            .NotEmpty().WithMessage("角色名不能为空")
            .MinimumLength(2).WithMessage("角色名至少需要2个字符")
            .MaximumLength(50).WithMessage("角色名最多50个字符")
            .Must(BeUniqueName).WithMessage("角色名已存在")
            .When(x => !x.IsEditMode || x.RoleName != _viewModel.RoleName);
        RuleFor(x => x.RoleCode)
            .NotEmpty().WithMessage("角色编码不能为空")
            .MinimumLength(2).WithMessage("角色编码至少需要2个字符")
            .MaximumLength(50).WithMessage("角色编码最多50个字符")
            .Matches(@"^[A-Za-z0-9_-]+$").WithMessage("角色编码只能包含字母、数字、下划线和中划线")
            .Must(BeUniqueCode).WithMessage("角色编码已存在")
            .When(x => !x.IsEditMode || x.RoleCode != _viewModel.RoleCode);

        RuleFor(x => x.SelectedStatus)
            .NotNull().WithMessage("请选择角色状态");
    }

    private bool BeUniqueCode(string roleCode)
    {
        if (string.IsNullOrWhiteSpace(roleCode)) return true;
        return true; // 角色编码唯一性检查将在数据库层面进行
    }
    
    private bool BeUniqueName(string roleName)
    {
        if (string.IsNullOrWhiteSpace(roleName)) return true;
        return true; // 角色名唯一性检查将在数据库层面进行
    }
}