<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="using:FluentAvalonia.UI.Controls"
             xmlns:entity="clr-namespace:IOTPlatform.ORM.Entity;assembly=IOTPlatform.ORM"
             xmlns:vm="clr-namespace:IOTPlatform.ViewModels"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:converter="clr-namespace:IOTPlatform.Shared.Converter;assembly=IOTPlatform.Shared"
             x:DataType="vm:RolesViewModel"
             prism:ViewModelLocator.AutoWireViewModel="True"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="IOTPlatform.Views.RolesView">
<UserControl.Resources>
        <converter:IndexToRowNumberConverter x:Key="IndexToRowNumberConverter"/>
        <converter:StatusConverter x:Key="StatusConverter"/>
    </UserControl.Resources>
    <ui:SettingsExpander Header="角色配置"
                         Description="配置系统角色信息"
                         IsExpanded="True">
        <ui:SettingsExpander.Footer>
            <Button>
                <ui:FontIcon Glyph="&#xE10C;"
                            FontFamily="{StaticResource SymbolThemeFontFamily}"
                            FontSize="28"/>
                <Button.Flyout>
                    <ui:FAMenuFlyout Placement="Bottom">
                        <ui:MenuFlyoutItem Text="新增角色" 
                                          Command="{Binding AddCommand}">
                            <ui:MenuFlyoutItem.IconSource>
                                <ui:SymbolIconSource Symbol="Add"/>
                            </ui:MenuFlyoutItem.IconSource>
                        </ui:MenuFlyoutItem>
                        <ui:MenuFlyoutItem Text="编辑角色"
                                          Command="{Binding EditCommand}"
                                          IsEnabled="{Binding IsRoleSelected}">
                            <ui:MenuFlyoutItem.IconSource>
                                <ui:SymbolIconSource Symbol="Edit"/>
                            </ui:MenuFlyoutItem.IconSource>
                        </ui:MenuFlyoutItem>
                        <ui:MenuFlyoutItem Text="删除角色"
                                          Command="{Binding DeleteCommand}"
                                          IsEnabled="{Binding IsRoleSelected}">
                            <ui:MenuFlyoutItem.IconSource>
                                <ui:SymbolIconSource Symbol="Delete"/>
                            </ui:MenuFlyoutItem.IconSource>
                        </ui:MenuFlyoutItem>
                        <ui:MenuFlyoutItem Text="编辑角色权限"
                                           Command="{Binding PermitCommand}"
                                           IsEnabled="{Binding IsRoleSelected}">
                            <ui:MenuFlyoutItem.IconSource>
                                <ui:SymbolIconSource Symbol="Delete"/>
                            </ui:MenuFlyoutItem.IconSource>
                        </ui:MenuFlyoutItem>
                    </ui:FAMenuFlyout>
                </Button.Flyout>
            </Button>
        </ui:SettingsExpander.Footer>
        <ui:SettingsExpanderItem>
            <DataGrid SelectionMode="Single"
                      SelectedItem="{Binding SelectedRole}"
                      ItemsSource="{Binding Roles}"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="序号" Width="*">
                        <DataGridTextColumn.Binding>
                            <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                     Path="Index" Mode="OneWay"
                                     Converter="{StaticResource IndexToRowNumberConverter}"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>
                    <DataGridTextColumn x:DataType="entity:SysRole" Header="角色名" 
                                        Binding="{Binding role_name}" Width="2*"/>
                    <DataGridTextColumn x:DataType="entity:SysRole" Header="角色代码" 
                                        Binding="{Binding role_code}" Width="*"/>
                    <DataGridTextColumn x:DataType="entity:SysRole" Header="状态" 
                                      Binding="{Binding status, Mode=OneWay, 
                                      Converter={StaticResource StatusConverter}}" Width="*"/>
                    <DataGridTextColumn x:DataType="entity:SysRole" Header="创建时间" 
                                      Binding="{Binding create_time, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" Width="2*"/>
                </DataGrid.Columns>
            </DataGrid>
        </ui:SettingsExpanderItem>
    </ui:SettingsExpander>
</UserControl>
