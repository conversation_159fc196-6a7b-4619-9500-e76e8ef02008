<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:IOTPlatform.ViewModels"
             x:DataType="vm:AddUserDialogViewModel"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="IOTPlatform.Views.AddUserDialog">
<StackPanel Spacing="10" Width="400">
        <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="100,*">
            <TextBlock Text="用户名："
                     Grid.Row="0" Grid.Column="0"
                     VerticalAlignment="Center"/>
            <TextBox Text="{Binding Username}"
                   Grid.Row="0" Grid.Column="1"
                   Watermark="请输入用户名"
                   IsEnabled="{Binding !IsEditMode}"
                   UseFloatingWatermark="True">
                <TextBox.Styles>
                    <Style Selector="TextBox:error /template/ Border#PART_BorderElement">
                        <Setter Property="BorderBrush" Value="Red"/>
                    </Style>
                </TextBox.Styles>
            </TextBox>
            <TextBlock Text="{Binding UsernameError}"
                     Grid.Row="1" Grid.Column="1"
                     Foreground="Red"
                     FontSize="12"
                     IsVisible="{Binding UsernameError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
        </Grid>

        <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="100,*">
            <TextBlock Text="密码："
                     Grid.Row="0" Grid.Column="0"
                     VerticalAlignment="Center"/>
            <TextBox Text="{Binding Password}"
                   Grid.Row="0" Grid.Column="1"
                   PasswordChar="*"
                   Watermark="请输入密码"
                   UseFloatingWatermark="True">
                <TextBox.Styles>
                    <Style Selector="TextBox:error /template/ Border#PART_BorderElement">
                        <Setter Property="BorderBrush" Value="Red"/>
                    </Style>
                </TextBox.Styles>
            </TextBox>
            <TextBlock Text="{Binding PasswordError}"
                     Grid.Row="1" Grid.Column="1"
                     Foreground="Red"
                     FontSize="12"
                     IsVisible="{Binding PasswordError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
        </Grid>

        <Grid ColumnDefinitions="100,*">
            <TextBlock Text="昵称："
                     Grid.Column="0"
                     VerticalAlignment="Center"/>
            <TextBox Text="{Binding Nickname}"
                   Grid.Column="1"
                   Watermark="请输入昵称"
                   UseFloatingWatermark="True"/>
        </Grid>

        <Grid ColumnDefinitions="100,*" RowDefinitions="Auto,Auto">
            <TextBlock Text="用户状态："
                     Grid.Column="0"
                     VerticalAlignment="Center"/>
            <ComboBox SelectedIndex="{Binding Status}"
                    Grid.Column="1"
                    HorizontalAlignment="Stretch">
                <ComboBoxItem Content="启用"/>
                <ComboBoxItem Content="禁用"/>
            </ComboBox>
            <TextBlock Text="{Binding StatusError}"
                       Grid.Row="1" Grid.Column="1"
                       Foreground="Red"
                       FontSize="12"
                       IsVisible="{Binding StatusError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
        </Grid>

        <Grid ColumnDefinitions="100,*" RowDefinitions="Auto,Auto">
            <TextBlock Text="角色选择："
                     Grid.Column="0"
                     VerticalAlignment="Top"/>
            <ItemsControl ItemsSource="{Binding Roles}"
                        Grid.Column="1">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <CheckBox Content="{Binding RoleName}"
                                IsChecked="{Binding IsChecked}"
                                Margin="0,2"/>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
            <TextBlock Text="{Binding RoleError}"
                       Grid.Row="1" Grid.Column="1"
                       Foreground="Red"
                       FontSize="12"
                       IsVisible="{Binding RoleError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
        </Grid>
    </StackPanel>
</UserControl>
