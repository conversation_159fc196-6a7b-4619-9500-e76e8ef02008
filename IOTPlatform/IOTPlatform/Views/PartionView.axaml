<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:ui="using:FluentAvalonia.UI.Controls" 
             xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
             xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
             xmlns:models="clr-namespace:IOTPlatform.Shared.Models;assembly=IOTPlatform.Shared"
             xmlns:vm="clr-namespace:IOTPlatform.ViewModels"
             xmlns:shared="clr-namespace:IOTPlatform.Shared"
             xmlns:shared1="clr-namespace:IOTPlatform.Shared;assembly=IOTPlatform.Shared"
             x:DataType="vm:PartionViewModel"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="IOTPlatform.Views.PartionView">
    <Grid>
        <ui:NavigationView x:Name="NvSample1" PaneDisplayMode="Left" 
                               OpenPaneLength="180" 
                               IsSettingsVisible="False" 
                               IsPaneToggleButtonVisible="False" IsPaneOpen="True"
                               MenuItemsSource="{Binding Menus}">
            <i:Interaction.Behaviors>
                <ia:EventTriggerBehavior EventName="SelectionChanged" SourceObject="NvSample1" >
                    <ia:InvokeCommandAction Command="{Binding SelectionChangedCommand}" PassEventArgsToCommand="True" />
                </ia:EventTriggerBehavior>
            </i:Interaction.Behaviors>
            <ui:NavigationView.MenuItemTemplateSelector>
                <models:MenuItemTemplateSelector>
                    <DataTemplate DataType="{x:Type models:WMenuItem}">
                        <ui:NavigationViewItem Content="{Binding Title}"
                                               FontWeight="Normal"
                                               IconSource="{Binding Icon}"
                                               ToolTip.Tip="{Binding ToolTip}"
                                               MenuItemsSource="{Binding MenuItems}"/>
                    </DataTemplate>
                    <models:MenuItemTemplateSelector.HeaderTemplate>
                        <DataTemplate DataType="{x:Type models:Header}">
                            <ui:NavigationViewItemHeader Content="{Binding Title}" />
                        </DataTemplate>
                    </models:MenuItemTemplateSelector.HeaderTemplate>
                    <models:MenuItemTemplateSelector.SeperatorTemplate>
                        <DataTemplate DataType="{x:Type models:Seperator}">
                            <ui:NavigationViewItemSeparator />
                        </DataTemplate>
                    </models:MenuItemTemplateSelector.SeperatorTemplate>
                </models:MenuItemTemplateSelector>
            </ui:NavigationView.MenuItemTemplateSelector>
            <ScrollViewer Margin="8">
                <StackPanel Spacing="8" prism:RegionManager.RegionName="{x:Static shared1:RegionNames.PartionRegion}">
                </StackPanel>
            </ScrollViewer>
            </ui:NavigationView>
    </Grid>
</UserControl>
