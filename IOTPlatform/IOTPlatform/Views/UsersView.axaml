<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:ui="using:FluentAvalonia.UI.Controls" 
             prism:ViewModelLocator.AutoWireViewModel="True"
             xmlns:vm="clr-namespace:IOTPlatform.ViewModels"
             xmlns:converter="clr-namespace:IOTPlatform.Shared.Converter;assembly=IOTPlatform.Shared"
             xmlns:entity="clr-namespace:IOTPlatform.ORM.Entity;assembly=IOTPlatform.ORM"
             x:DataType="vm:UsersViewModel"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="IOTPlatform.Views.UsersView">
    <UserControl.Resources>
        <converter:IndexToRowNumberConverter x:Key="IndexToRowNumberConverter"/>
        <converter:StatusConverter x:Key="StatusConverter"/>
    </UserControl.Resources>
    <UserControl.Styles>
        <Style Selector="FlyoutPresenter.test">
            <Setter Property="Background" Value="White"/>
        </Style>
    </UserControl.Styles>
    <ui:SettingsExpander Header="用户账号配置"
                         Description="配置系统账号信息"
                         IsExpanded="True">
        <ui:SettingsExpander.Footer>
            <Button Width="40" Height="40">
                <ui:FontIcon Glyph="&#xE10C;"
                             FontFamily="{StaticResource SymbolThemeFontFamily}"
                             FontSize="28"/>
                <Button.Flyout>
                    <ui:FAMenuFlyout ShowMode="Transient" Placement="Bottom">
                    <ui:MenuFlyoutItem Text="新增用户"
                                       Command="{Binding AddCommand}">
                        <ui:MenuFlyoutItem.IconSource>
                            <ui:SymbolIconSource Symbol="Add" />
                        </ui:MenuFlyoutItem.IconSource>
                    </ui:MenuFlyoutItem>
                    <ui:MenuFlyoutItem Text="编辑用户"
                                       Command="{Binding EditCommand}"
                                       IsEnabled="{Binding IsUserSelected}">
                        <ui:MenuFlyoutItem.IconSource>
                            <ui:SymbolIconSource Symbol="Edit" />
                        </ui:MenuFlyoutItem.IconSource>
                    </ui:MenuFlyoutItem>
                    <ui:MenuFlyoutItem Text="删除用户"
                                       Command="{Binding DeleteCommand}"
                                       IsEnabled="{Binding IsUserSelected}">
                        <ui:MenuFlyoutItem.IconSource>
                            <ui:SymbolIconSource Symbol="Delete" />
                        </ui:MenuFlyoutItem.IconSource>
                    </ui:MenuFlyoutItem>
                </ui:FAMenuFlyout>
                </Button.Flyout>
            </Button>
        </ui:SettingsExpander.Footer>
        <ui:SettingsExpanderItem>
            <DataGrid Name="TargetDataGrid" SelectionMode="Single"
                      SelectedItem="{Binding SelectedUser}"
                      ItemsSource="{Binding Users}"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="序号" Width="*">
                        <DataGridTextColumn.Binding>
                            <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                     Path="Index" Mode="OneWay"
                                     Converter="{StaticResource IndexToRowNumberConverter}"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>
                    <DataGridTextColumn x:DataType="entity:SysUser" Header="用户名" Binding="{Binding username}" Width="2*" />
                    <DataGridTextColumn x:DataType="entity:SysUser" Header="密码" Binding="{Binding password}" Width="2*" />
                    <DataGridTextColumn x:DataType="entity:SysUser" Header="角色" Binding="{Binding RoleNames}" Width="2*" />
                    <DataGridTextColumn x:DataType="entity:SysRole" Header="状态"
                                        Binding="{Binding status, Mode=OneWay,
                                      Converter={StaticResource StatusConverter}}" Width="*"/>
                    <DataGridTextColumn x:DataType="entity:SysUser" DisplayIndex="10" Header="创建时间" Binding="{Binding create_time, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" Width="3*" />
                </DataGrid.Columns>
            </DataGrid>
        </ui:SettingsExpanderItem>
    </ui:SettingsExpander>
</UserControl>
