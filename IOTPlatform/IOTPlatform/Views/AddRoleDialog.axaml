<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:IOTPlatform.ViewModels"
             x:DataType="vm:AddRoleDialogViewModel"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="IOTPlatform.Views.AddRoleDialog">
<StackPanel Spacing="10" Width="300">
        <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="100,*">
            <TextBlock Text="角色名："
                     Grid.Row="0" Grid.Column="0"
                     VerticalAlignment="Center"/>
            <TextBox Text="{Binding RoleName}"
                   Grid.Row="0" Grid.Column="1"
                   Watermark="请输入角色名"
                   IsEnabled="{Binding !IsEditMode}"
                   UseFloatingWatermark="True">
                <TextBox.Styles>
                    <Style Selector="TextBox:error /template/ Border#PART_BorderElement">
                        <Setter Property="BorderBrush" Value="Red"/>
                    </Style>
                </TextBox.Styles>
            </TextBox>
            <TextBlock Text="{Binding RoleNameError}"
                     Grid.Row="1" Grid.Column="1"
                     Foreground="Red"
                     FontSize="12"
                     IsVisible="{Binding RoleNameError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
        </Grid>
        <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="100,*">
            <TextBlock Text="角色编码："
                       Grid.Row="0" Grid.Column="0"
                       VerticalAlignment="Center"/>
            <TextBox Text="{Binding RoleCode}"
                     Grid.Row="0" Grid.Column="1"
                     Watermark="请输入角色编码"
                     IsEnabled="{Binding !IsEditMode}"
                     UseFloatingWatermark="True">
                <TextBox.Styles>
                    <Style Selector="TextBox:error /template/ Border#PART_BorderElement">
                        <Setter Property="BorderBrush" Value="Red"/>
                    </Style>
                </TextBox.Styles>
            </TextBox>
            <TextBlock Text="{Binding RoleCodeError}"
                       Grid.Row="1" Grid.Column="1"
                       Foreground="Red"
                       FontSize="12"
                       IsVisible="{Binding RoleCodeError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
        </Grid>
        <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="100,*">
            <TextBlock Text="状态："
                     Grid.Row="0" Grid.Column="0"
                     VerticalAlignment="Center"/>
            <ComboBox ItemsSource="{Binding Statuses}"
                    SelectedItem="{Binding SelectedStatus}"
                    Grid.Row="0" Grid.Column="1"
                    HorizontalAlignment="Stretch">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
                <ComboBox.Styles>
                    <Style Selector="ComboBox:error /template/ Border#PART_BorderElement">
                        <Setter Property="BorderBrush" Value="Red"/>
                    </Style>
                </ComboBox.Styles>
            </ComboBox>
            <TextBlock Text="{Binding StatusError}"
                     Grid.Row="1" Grid.Column="1"
                     Foreground="Red"
                     FontSize="12"
                     IsVisible="{Binding StatusError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
        </Grid>
    </StackPanel>
</UserControl>
