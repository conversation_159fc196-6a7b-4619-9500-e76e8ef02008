<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:IOTPlatform.ViewModels"
             xmlns:ui="using:FluentAvalonia.UI.Controls" 
             xmlns:prism="http://prismlibrary.com/"
             xmlns:shared="clr-namespace:IOTPlatform.Shared;assembly=IOTPlatform.Shared"
             xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
             xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
             xmlns:models="clr-namespace:IOTPlatform.Shared.Models;assembly=IOTPlatform.Shared"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="IOTPlatform.Views.MainView"
             x:DataType="vm:MainViewModel">
    <Grid RowDefinitions="Auto,*">
        <Grid Name="TitleBarHost"
              ColumnDefinitions="Auto,Auto,*,Auto" 
              Background="Transparent">
            <Image Margin="12 4"
                   IsHitTestVisible="False"
                   Source="/Assets/avalonia-logo.ico"
                   Width="18" Height="18"
                   DockPanel.Dock="Left"
                   Name="WindowIcon"
                   RenderOptions.BitmapInterpolationMode="HighQuality">
                <Image.IsVisible>
                    <OnPlatform Default="False">
                        <On Options="Windows" Content="True" />
                    </OnPlatform>
                </Image.IsVisible>
            </Image>
            <TextBlock Text="{Binding Title, RelativeSource={RelativeSource FindAncestor, AncestorType=Window}}"
                       FontSize="12"
                       IsHitTestVisible="False"
                       VerticalAlignment="Center"
                       Grid.Column="1">
                <TextBlock.IsVisible>
                    <OnPlatform Default="False">
                        <On Options="Windows" Content="True" />
                    </OnPlatform>
                </TextBlock.IsVisible>
            </TextBlock>
        </Grid>
        <Grid Grid.Row="1">
            <ui:NavigationView x:Name="NvSample1" PaneDisplayMode="Left" 
                               OpenPaneLength="180" 
                               IsSettingsVisible="True" 
                               IsPaneToggleButtonVisible="True" IsPaneOpen="True"
                               MenuItemsSource="{Binding Menus}">
                <i:Interaction.Behaviors>
                    <ia:EventTriggerBehavior EventName="SelectionChanged" SourceObject="NvSample1" >
                        <ia:InvokeCommandAction Command="{Binding SelectionChangedCommand}" PassEventArgsToCommand="True" />
                    </ia:EventTriggerBehavior>
                </i:Interaction.Behaviors>
                <ui:NavigationView.MenuItemTemplateSelector>
                    <models:MenuItemTemplateSelector>
                        <DataTemplate DataType="{x:Type models:WMenuItem}">
                            <ui:NavigationViewItem Content="{Binding Title}"
                                                   FontWeight="Normal"
                                                   IconSource="{Binding Icon}"
                                                   ToolTip.Tip="{Binding ToolTip}"
                                                   MenuItemsSource="{Binding MenuItems}"/>
                        </DataTemplate>
                        <models:MenuItemTemplateSelector.HeaderTemplate>
                            <DataTemplate DataType="{x:Type models:Header}">
                                <ui:NavigationViewItemHeader Content="{Binding Title}" />
                            </DataTemplate>
                        </models:MenuItemTemplateSelector.HeaderTemplate>
                        <models:MenuItemTemplateSelector.SeperatorTemplate>
                            <DataTemplate DataType="{x:Type models:Seperator}">
                                <ui:NavigationViewItemSeparator />
                            </DataTemplate>
                        </models:MenuItemTemplateSelector.SeperatorTemplate>
                    </models:MenuItemTemplateSelector>
                </ui:NavigationView.MenuItemTemplateSelector>
                <ui:Frame Name="FrameView" prism:RegionManager.RegionName="{x:Static shared:RegionNames.ContentRegion}"/> 
            </ui:NavigationView>
        </Grid>
        
    </Grid>
</UserControl>
