<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="using:FluentAvalonia.UI.Controls"
             xmlns:vm="clr-namespace:IOTPlatform.ViewModels"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             x:DataType="vm:AppThemeViewModel"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="IOTPlatform.Views.AppThemeView">
    <ui:SettingsExpander Header="系统主题"
                         IconSource="DarkTheme"
                         Description="配置系统主题信息"
                         IsExpanded="True">
        <ui:SettingsExpander.Footer>
            <ComboBox SelectedItem="{Binding CurrentAppTheme}"
                      ItemsSource="{Binding AppThemes}"
                      MinWidth="150" />
        </ui:SettingsExpander.Footer>
    </ui:SettingsExpander>
</UserControl>
