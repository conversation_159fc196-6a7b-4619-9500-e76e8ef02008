<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             xmlns:vm="using:IOTPlatform.ViewModels"
             x:DataType="vm:RoleMenuDialogViewModel"
             xmlns:prism="http://prismlibrary.com/"
             d:DataContext="{d:DesignInstance Type=vm:RoleMenuDialogViewModel}"
             x:Class="IOTPlatform.Views.RoleMenuDialog">
<Grid RowDefinitions="Auto,*,Auto" Margin="20">
        <!-- 头部说明文字 -->
        <TextBlock Grid.Row="0"
                 Text="请选择要分配的菜单权限："
                 Theme="{StaticResource BodyStrongTextBlockStyle}"
                 Margin="0,0,0,15"/>

        <!-- 菜单树列表 -->
        <ScrollViewer Grid.Row="1"
                     HorizontalScrollBarVisibility="Disabled"
                     VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding MenuNodes}">
                <ItemsControl.Styles>
                    <Style Selector="CheckBox">
                        <Setter Property="Margin" Value="0,5"/>
                    </Style>
                </ItemsControl.Styles>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <StackPanel>
                            <!-- 父级菜单 -->
                            <CheckBox Content="{Binding Title}"
                                    IsChecked="{Binding IsChecked}"
                                    FontWeight="SemiBold"/>
                            <!-- 子菜单列表 -->
                            <ItemsControl ItemsSource="{Binding Children}"
                                        Margin="25,0,0,5"
                                        IsVisible="{Binding Children.Count}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <CheckBox Content="{Binding Title}"
                                                IsChecked="{Binding IsChecked}"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
        <!-- 验证消息 -->
        <TextBlock Grid.Row="2"
                   Text="{Binding ValidationMessage}"
                   Foreground="Red"
                   IsVisible="{Binding HasValidationError}"
                   Margin="0,10,0,0"/>
    </Grid>
</UserControl>
