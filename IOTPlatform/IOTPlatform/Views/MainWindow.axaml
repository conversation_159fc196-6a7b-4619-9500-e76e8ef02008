<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:IOTPlatform.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:IOTPlatform.Views"
        xmlns:prism="http://prismlibrary.com/"
        prism:ViewModelLocator.AutoWireViewModel="True"
        xmlns:shared="clr-namespace:IOTPlatform.Shared;assembly=IOTPlatform.Shared"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="IOTPlatform.Views.MainWindow"
        Icon="/Assets/avalonia-logo.ico"
        Title="IOTPlatform">
        <ContentControl prism:RegionManager.RegionName="{x:Static shared:RegionNames.WindowRegion}" />
</Window>
