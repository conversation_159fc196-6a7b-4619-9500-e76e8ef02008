<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="IOTPlatform.Views.LoginView"
        xmlns:vm="using:IOTPlatform.ViewModels"
        x:DataType="vm:LoginViewModel"
        Width="450"
        Height="380"
        WindowStartupLocation="CenterScreen"
        CanResize="False"
        Title="系统登录">
   <Grid>
        <Border Width="400"
                Height="320"
                Background="{DynamicResource ApplicationBackgroundBrush}"
                CornerRadius="8"
                BoxShadow="0 4 8 0 #20000000">
            <StackPanel Margin="40" Spacing="20">
                <TextBlock Text="系统登录"
                         HorizontalAlignment="Center"
                         FontSize="24"
                         FontWeight="Bold"
                         Margin="0,0,0,20"/>
                
                <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="Auto,*">
                    <TextBlock Text="用户名："
                             Grid.Row="0" Grid.Column="0"
                             VerticalAlignment="Center"
                             Width="70"/>
                    <TextBox Text="{Binding Username}"
                            Grid.Row="0" Grid.Column="1"
                            Watermark="请输入用户名"/>
                    <TextBlock Text="{Binding UsernameError}"
                             Grid.Row="1" Grid.Column="1"
                             Foreground="Red"
                             FontSize="12"
                             IsVisible="{Binding UsernameError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                </Grid>

                <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="Auto,*">
                    <TextBlock Text="密码："
                             Grid.Row="0" Grid.Column="0"
                             VerticalAlignment="Center"
                             Width="70"/>
                    <TextBox Text="{Binding Password}"
                            Grid.Row="0" Grid.Column="1"
                            PasswordChar="●"
                            Watermark="请输入密码"/>
                    <TextBlock Text="{Binding PasswordError}"
                             Grid.Row="1" Grid.Column="1"
                             Foreground="Red"
                             FontSize="12"
                             IsVisible="{Binding PasswordError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                </Grid>

                <Button Content="登 录"
                        Command="{Binding LoginCommand}"
                        HorizontalAlignment="Stretch"
                        HorizontalContentAlignment="Center"
                        Classes="accent"
                        Height="40"
                        Margin="0,20,0,0"/>

                <TextBlock Text="{Binding ErrorMessage}"
                         Foreground="Red"
                         FontSize="12"
                         HorizontalAlignment="Center"
                         IsVisible="{Binding ErrorMessage, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
