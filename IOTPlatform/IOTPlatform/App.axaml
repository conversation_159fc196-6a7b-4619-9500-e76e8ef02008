<prism:PrismApplication xmlns="https://github.com/avaloniaui"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        xmlns:local="using:IOTPlatform"
                        xmlns:prism="http://prismlibrary.com/"
                        xmlns:sty="using:FluentAvalonia.Styling"
                        x:Class="IOTPlatform.App"
                        RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

    <Application.Styles>
     <sty:FluentAvaloniaTheme PreferSystemTheme="True" PreferUserAccentColor="True" />
     <sty:FluentAvaloniaTheme />
    </Application.Styles>
             
</prism:PrismApplication>