using System;
using System.Diagnostics;
using System.IO;
using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Data.Core;
using Avalonia.Data.Core.Plugins;
using System.Linq;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using IOTPlatform.Controls;
using IOTPlatform.InfluxDbWrapper.Models;
using IOTPlatform.InfluxDbWrapper.Services;
using IOTPlatform.ORM.DbContext;
using IOTPlatform.ORM.Repository;
using IOTPlatform.Shared.Services;
using IOTPlatform.ViewModels;
using IOTPlatform.Views;
using Microsoft.Extensions.Configuration;
using Prism.DryIoc;
using Prism.Ioc;
using Prism.Modularity;
using Prism.Navigation.Regions;
using Serilog;
using Serilog.Events;

namespace IOTPlatform;

public partial class App : PrismApplication
{
    private ILogger _logger = null!;
    private IConfigurationRoot _configuration = null!;
    private InfluxDbConfiguration _influxConfig = null!;
    private void ConfigureSerilog()
    {
        var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", "log-.txt");
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .WriteTo.File(logPath,
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 30,
                outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();
        _logger = Log.Logger;
    }
    
    private void ConfigureInfluxDb()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        _configuration = builder.Build();
        var influxConfig = new InfluxDbConfiguration();
        _configuration.GetSection("InfluxDbConfiguration").Bind(influxConfig);
        _influxConfig = influxConfig;
    }
    
    private void CurrentOnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        _logger.Warning(e.Exception.Message, "Application Error");

        if (Debugger.IsAttached)
            e.Handled = false;
        else
            ShowUnhandledException(e);
    }

    private static void ShowUnhandledException(DispatcherUnhandledExceptionEventArgs e)
    {
        // ... (rest of the existing ShowUnhandledException method remains the same)
    }
    
    public App()
    {
        // Configure Serilog
        ConfigureSerilog();
        ConfigureInfluxDb();
        Dispatcher.UIThread.UnhandledException += CurrentOnDispatcherUnhandledException;
    }
    
    protected override void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Register Serilog
        containerRegistry.RegisterInstance<ILogger>(Log.Logger);
        // 注册 IConfiguration
        containerRegistry.RegisterInstance<IConfiguration>(_configuration);
        // 注册 InfluxDbConfiguration
        containerRegistry.RegisterInstance(_influxConfig);

        // Services
        containerRegistry.RegisterSingleton<DbContext>();
        containerRegistry.RegisterSingleton<IInfluxDbService, InfluxDbService>();
        // Views - Region Navigation
        containerRegistry.RegisterForNavigation<MainView, MainViewModel>();
        containerRegistry.RegisterForNavigation<WellcomeView, WellcomeViewModel>();
        containerRegistry.RegisterForNavigation<PartionView, PartionViewModel>();
        containerRegistry.RegisterForNavigation<UsersView, UsersViewModel>();
        containerRegistry.RegisterForNavigation<RolesView, RolesViewModel>();
        containerRegistry.RegisterForNavigation<AppThemeView, AppThemeViewModel>();

        // Services
        containerRegistry.RegisterSingleton<IMenuService, MenuService>();
        containerRegistry.RegisterSingleton<IUserService, UserService>();
        containerRegistry.Register<UserRepository>();
        containerRegistry.Register<RoleRepository>();
        containerRegistry.Register<MenuRepository>();
        containerRegistry.Register<SysRoleMenuRepository>();
        containerRegistry.Register<SysUserRoleRepository>();
    }

    protected override AvaloniaObject CreateShell()
    {
        _logger.Information("Application starting...");
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            // Desktop applications
            DisableAvaloniaDataAnnotationValidation();
            desktop.ShutdownMode = ShutdownMode.OnLastWindowClose;
            return Container.Resolve<MainWindow>();
        }
        else
        {
            // Mobile and WebBrowser
            return Container.Resolve<MainView>();
        }
    }

    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
        base.Initialize();
    }

    protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
    {
        // moduleCatalog.AddModule<DEMOModule>(
        //     nameof(DEMOModule),
        //     InitializationMode.WhenAvailable);
    }

    protected override void ConfigureRegionAdapterMappings(RegionAdapterMappings regionAdapterMappings)
    {
        base.ConfigureRegionAdapterMappings(regionAdapterMappings);
        regionAdapterMappings.RegisterMapping(typeof(StackPanel), Container.Resolve<StackPanelRegionAdapter>());
    }

    private void DisableAvaloniaDataAnnotationValidation()
    {
        // Get an array of plugins to remove
        var dataValidationPluginsToRemove =
            BindingPlugins.DataValidators.OfType<DataAnnotationsValidationPlugin>().ToArray();

        // remove each entry found
        foreach (var plugin in dataValidationPluginsToRemove)
        {
            BindingPlugins.DataValidators.Remove(plugin);
        }
    }
}