using System;
using System.Linq;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using IOTPlatform.Shared;
using IOTPlatform.Shared.Services;
using IOTPlatform.Views;
using Prism.Ioc;

namespace IOTPlatform.ViewModels;

public partial class LoginViewModel : ViewModelBase
{
    private readonly IUserService _userService;
    private readonly IContainerProvider _container;
    
    [ObservableProperty]
    private string _username = string.Empty;

    [ObservableProperty]
    private string _password = string.Empty;

    [ObservableProperty]
    private string _usernameError = string.Empty;

    [ObservableProperty]
    private string _passwordError = string.Empty;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    public LoginViewModel(IUserService userService, IContainerProvider container)
    {
        _container = container;
        _userService = userService;
        Username = "admin";
        Password = "111111";
    }

    private bool ValidateInput()
    {
        UsernameError = string.Empty;
        PasswordError = string.Empty;
        ErrorMessage = string.Empty;

        var isValid = true;

        if (string.IsNullOrWhiteSpace(Username))
        {
            UsernameError = "请输入用户名";
            isValid = false;
        }

        if (string.IsNullOrWhiteSpace(Password))
        {
            PasswordError = "请输入密码";
            isValid = false;
        }

        return isValid;
    }

    [RelayCommand]
    private async Task LoginAsync()
    {
        if (!ValidateInput())
            return;

        try
        {
            var loginResult = await _userService.LoginAsync(Username, Password);
            if (loginResult.Success)
            {
                if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
                {
                    var loginWindow = desktop.Windows.FirstOrDefault(w => w is LoginView);
                    if (loginWindow != null)
                    {
                        ((Window)loginWindow).Close(true);
                    }
                }
            }
            else
            {
                ErrorMessage = loginResult.Message;
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = "登录失败：" + ex.Message;
        }
    }
}