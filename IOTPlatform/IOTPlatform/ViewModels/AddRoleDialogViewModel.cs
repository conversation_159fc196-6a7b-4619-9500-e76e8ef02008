using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using FluentValidation;
using IOTPlatform.ORM.Entity;
using IOTPlatform.ORM.Repository;
using IOTPlatform.Shared;
using IOTPlatform.Validator;

namespace IOTPlatform.ViewModels;

public partial class AddRoleDialogViewModel : ViewModelBase, IValidatableObject
{
    private readonly RoleRepository _roleRepository;
    
    [ObservableProperty]
    private string _roleName = string.Empty;

    [ObservableProperty]
    private string _roleNameError = string.Empty;

    [ObservableProperty]
    private StatusItem? _selectedStatus;

    [ObservableProperty]
    private string _statusError = string.Empty;

    [ObservableProperty]
    private bool _isEditMode;
    
    [ObservableProperty]
    private string _roleCode = string.Empty;

    [ObservableProperty]
    private string _roleCodeError = string.Empty;

    private long? _roleId;

    private readonly IValidator<AddRoleDialogViewModel> _validator;

    public ObservableCollection<StatusItem> Statuses { get; } = new()
    {
        new StatusItem(0, "启用"),
        new StatusItem(1, "禁用")
    };
    
    public bool ValidateInput()
    {
        if (string.IsNullOrWhiteSpace(RoleName))
        {
            RoleNameError = "请输入角色名称";
            return false;
        }
        if (string.IsNullOrWhiteSpace(RoleCode))
        {
            RoleCodeError = "请输入角色代码";
            return false;
        }
        // 状态验证
        if (SelectedStatus != null)
        {
            if (SelectedStatus.Value != 0 && SelectedStatus.Value != 1)
            {
                StatusError = "请选择正确的状态";
                return false;
            }
        }
        else
        {
            StatusError = "请选择正确的状态";
            return false;
        }
        RoleNameError = string.Empty;
        RoleCodeError = string.Empty;
        StatusError = string.Empty;
        return true;
    }

    public AddRoleDialogViewModel(RoleRepository roleRepository, SysRole? role = null)
    {
        _roleRepository = roleRepository ?? throw new ArgumentNullException(nameof(roleRepository));
        _validator = new AddRoleDialogValidator(this);
        PropertyChanged += OnPropertyChanged;

        if (role != null)
        {
            IsEditMode = true;
            _roleId = role.role_id;
            RoleName = role.role_name ?? string.Empty;
            RoleCode = role.role_code ?? string.Empty;
            SelectedStatus = Statuses.FirstOrDefault(s => s.Value == role.status);
        }
    }

    private async void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(RoleName) || 
            e.PropertyName == nameof(RoleCode) || 
            e.PropertyName == nameof(SelectedStatus))
        {
            await ValidateProperty(e.PropertyName);
        }
    }

    private async Task ValidateProperty(string? propertyName)
    {
        var context = new ValidationContext<AddRoleDialogViewModel>(this);
        var result = await _validator.ValidateAsync(context);

        if (propertyName == nameof(RoleName))
        {
            RoleNameError = result.Errors
                .Where(e => e.PropertyName == nameof(RoleName))
                .Select(e => e.ErrorMessage)
                .FirstOrDefault() ?? string.Empty;
        }
        
        if (propertyName == nameof(RoleCode))
        {
            RoleCodeError = result.Errors
                .Where(e => e.PropertyName == nameof(RoleCode))
                .Select(e => e.ErrorMessage)
                .FirstOrDefault() ?? string.Empty;
        }

        if (propertyName == nameof(SelectedStatus))
        {
            StatusError = result.Errors
                .Where(e => e.PropertyName == nameof(SelectedStatus))
                .Select(e => e.ErrorMessage)
                .FirstOrDefault() ?? string.Empty;
        }
    }

    public async Task<bool> SaveAsync()
    {
        var validationResult = await _validator.ValidateAsync(this);
        if (!validationResult.IsValid)
            return false;

        var role = ToRole();
        
        if (IsEditMode)
        {
            return await _roleRepository.UpdateAsync(role);
        }

        return await _roleRepository.CreateAsync(role);
    }

    private SysRole ToRole()
    {
        var role = new SysRole
        {
            role_name = RoleName.Trim(),
            role_code = RoleCode,
            status = SelectedStatus!.Value,
        };

        if (IsEditMode)
        {
            role.role_id = _roleId!.Value;
        }
        else
        {
            role.create_time = DateTime.Now;
        }

        return role;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var fluentValidationResult = _validator.Validate(this);
        return fluentValidationResult.Errors.Select(error =>
            new ValidationResult(error.ErrorMessage, new[] { error.PropertyName }));
    }
}

public record StatusItem(int Value, string Name);