﻿using System;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FluentAvalonia.UI.Controls;
using IOTPlatform.Shared;
using IOTPlatform.Shared.Models;
using IOTPlatform.Shared.Services;
using IOTPlatform.Views;
using Prism.Navigation;
using Prism.Navigation.Regions;

namespace IOTPlatform.ViewModels;

public partial class MainViewModel : ViewModelBase
{
    private readonly IRegionManager _regionManager;
    private readonly IMenuService _menuService;
    private readonly IUserService _userService;

    [ObservableProperty] private ObservableCollection<WMenuBase> _menus;

    public MainViewModel(IRegionManager regionManager, IMenuService menuService, IUserService userService)
    {
        _regionManager = regionManager;
        _menuService = menuService;
        _userService = userService;
        Menus = new ObservableCollection<WMenuBase>();
        LoadUserMenus();
    }
    
    private async void LoadUserMenus()
    {
        try
        {
            // 获取当前登录用户信息
            var currentUser = await _userService.GetCurrentUserAsync();
            if (currentUser == null)
            {
                return;
            }

            // 获取用户的权限菜单
            var userMenus = await _userService.GetUserFrontMenusAsync(currentUser.user_id);
            
            // 清空并添加新的菜单项
            Menus.Clear();
            Menus.AddRange(userMenus);
        }
        catch (Exception ex)
        {
            // 处理异常
            System.Diagnostics.Debug.WriteLine($"加载用户菜单失败: {ex.Message}");
        }
    }
    
    [RelayCommand]
    private void SelectionChanged(NavigationViewSelectionChangedEventArgs? e)
    {
        if (e?.SelectedItem is WMenuItem menuItem)
        {
            _regionManager.RequestNavigate(RegionNames.ContentRegion, menuItem.NavigationTarget);
        }
        else if (e?.SelectedItem is NavigationViewItem nvi && nvi.Content?.ToString() == "设置") {
            var para = new NavigationParameters();
            para.Add("Uri", "MainView");
            para.Add("Param", "设置");
            _regionManager.RequestNavigate(RegionNames.ContentRegion, nameof(PartionView), para);
        }
    }
}