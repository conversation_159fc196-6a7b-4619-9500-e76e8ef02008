using System;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using FluentAvalonia.UI.Controls;
using IOTPlatform.ORM.DbContext;
using IOTPlatform.ORM.Entity;
using IOTPlatform.ORM.Repository;
using IOTPlatform.Shared;
using IOTPlatform.Shared.Services;
using IOTPlatform.Views;
using Prism.Commands;

namespace IOTPlatform.ViewModels;

public partial class RolesViewModel: ViewModelBase
{
    private SysRole? _selectedRole;
    private readonly RoleRepository _roleRepository;
    private readonly IMenuService _menuService;
    [ObservableProperty]
    private ObservableCollection<SysRole> _roles;

    public SysRole? SelectedRole
    {
        get => _selectedRole;
        set
        {
            SetProperty(ref _selectedRole, value);
            OnPropertyChanged(nameof(IsRoleSelected));
            EditCommand.RaiseCanExecuteChanged();
            DeleteCommand.RaiseCanExecuteChanged();
        }
    }

    public bool IsRoleSelected => SelectedRole != null;

    public DelegateCommand AddCommand { get; }
    public DelegateCommand EditCommand { get; }
    public DelegateCommand DeleteCommand { get; }
    public DelegateCommand PermitCommand { get; }
    public RolesViewModel(RoleRepository roleRepository, IMenuService menuService, DbContext db)
    {
        _roleRepository = roleRepository;
        _menuService = menuService;
        AddCommand = new DelegateCommand(ExecuteAdd);
        EditCommand = new DelegateCommand(ExecuteEdit, () => IsRoleSelected);
        DeleteCommand = new DelegateCommand(ExecuteDelete, () => IsRoleSelected);
        PermitCommand = new DelegateCommand(ExecutePermit, () => IsRoleSelected);
        Roles = new ObservableCollection<SysRole>();
        InitializeAsync();
    }

    private async void InitializeAsync()
    {
        Roles = new ObservableCollection<SysRole>(
            await _roleRepository.GetRolesAsync()
        );
    }

    private async void ExecuteAdd()
    {
        var dialogViewModel = new AddRoleDialogViewModel(_roleRepository);
        var dialog = new ContentDialog
        {
            Title = "新增角色",
            PrimaryButtonText = "保存",
            CloseButtonText = "取消",
            Content = new AddRoleDialog { DataContext = dialogViewModel }
        };
        dialog.PrimaryButtonClick += (s, e) =>
        {
            if (!dialogViewModel.ValidateInput())
            {
                e.Cancel = true;
            }
        };
        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            if (await dialogViewModel.SaveAsync())
            {
                InitializeAsync();
            }
        }
    }

    private async void ExecuteEdit()
    {
        if (SelectedRole == null) return;

        var dialogViewModel = new AddRoleDialogViewModel(_roleRepository, SelectedRole);
        var dialog = new ContentDialog
        {
            Title = "编辑角色",
            PrimaryButtonText = "保存",
            CloseButtonText = "取消",
            Content = new AddRoleDialog { DataContext = dialogViewModel }
        };
        dialog.PrimaryButtonClick += (s, e) =>
        {
            if (!dialogViewModel.ValidateInput())
            {
                e.Cancel = true;
            }
        };
        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            if (await dialogViewModel.SaveAsync())
            {
                InitializeAsync();
            }
        }
    }

    private async void ExecuteDelete()
    {
        if (SelectedRole == null) return;

        var dialog = new ContentDialog
        {
            Title = "删除确认",
            Content = $"确定要删除角色 {SelectedRole.role_name} 吗？",
            PrimaryButtonText = "删除",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Close
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            try
            {
                await _roleRepository.DeleteAsync(SelectedRole.role_id);
                InitializeAsync();
            }
            catch (Exception ex)
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = $"删除角色失败：{ex.Message}",
                    CloseButtonText = "确定"
                };
                await errorDialog.ShowAsync();
            }
        }
    }
    
    private async void ExecutePermit()
    {
        if (SelectedRole == null) return;

        var dialogViewModel = new RoleMenuDialogViewModel(
            _menuService, 
            _roleRepository, 
            SelectedRole.role_id);

        var dialog = new ContentDialog
        {
            Title = "配置菜单权限",
            PrimaryButtonText = "保存",
            CloseButtonText = "取消",
            Content = new RoleMenuDialog { DataContext = dialogViewModel },
            DefaultButton = ContentDialogButton.Primary
        };
        
        dialog.PrimaryButtonClick += (s, e) =>
        {
            if (!dialogViewModel.ValidateSelection())
            {
                e.Cancel = true;
            }
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            try
            {
                await dialogViewModel.SaveAsync();
                var successDialog = new ContentDialog
                {
                    Title = "提示",
                    Content = "菜单权限配置已更新",
                    CloseButtonText = "确定"
                };
                await successDialog.ShowAsync();
            }
            catch (Exception ex)
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = $"保存失败：{ex.Message}",
                    CloseButtonText = "确定"
                };
                await errorDialog.ShowAsync();
            }
        }
    }
}