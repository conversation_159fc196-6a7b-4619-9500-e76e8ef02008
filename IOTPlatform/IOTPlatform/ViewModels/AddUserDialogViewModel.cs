using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using FluentValidation;
using IOTPlatform.ORM.Entity;
using IOTPlatform.ORM.Repository;
using IOTPlatform.Shared;
using IOTPlatform.Validator;

namespace IOTPlatform.ViewModels;

public partial class AddUserDialogViewModel: ViewModelBase, IValidatableObject
{
    private readonly UserRepository _userRepository;
    [ObservableProperty] 
    private string _username = string.Empty;
    [ObservableProperty] 
    private string _password = string.Empty;
    [ObservableProperty] 
    private string _nickname = string.Empty;
    [ObservableProperty]
    private ObservableCollection<RoleCheckItem> _roles = new();
    [ObservableProperty] 
    private byte _status = 0;
    [ObservableProperty]
    private string _usernameError = string.Empty;
    [ObservableProperty]
    private string _passwordError = string.Empty;
    [ObservableProperty]
    private string _roleError = string.Empty;
    [ObservableProperty]
    private string _statusError = string.Empty;
    private readonly IValidator<AddUserDialogViewModel> _validator;
    [ObservableProperty]
    private bool _isEditMode;
    
    private long? _userId;

    public bool ValidateInput()
    {
        // 用户名和密码验证
        if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
        {
            UsernameError = "请输入用户名和密码";
            return false;
        }
        // 用户名长度校验
        if (Username.Length < 3)
        {
            UsernameError = "用户名长度不能少于3位";
            return false;
        }
        // 密码长度校验
        if (Password.Length < 6)
        {
            PasswordError = "密码长度不能少于6位";
            return false;
        }
        // 角色验证
        if (!Roles.Any(r => r.IsChecked))
        {
            RoleError = "请至少选择一个角色";
            return false;
        }
        // 状态验证
        if (Status != 0 && Status != 1)
        {
            StatusError = "请选择正确的状态";
            return false;
        }
        UsernameError = string.Empty;
        PasswordError = string.Empty;
        RoleError = string.Empty;
        StatusError = string.Empty;
        return true;
    }
    
    public class RoleCheckItem
    {
        public long RoleId { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public bool IsChecked { get; set; }
    }
    
    public AddUserDialogViewModel(UserRepository userRepository, SysUser? user = null)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _validator = new AddUserValidator();
        this.PropertyChanged += OnPropertyChanged;

        if (user != null)
        {
            IsEditMode = true;
            _userId = user.user_id;
            Username = user.username ?? string.Empty;
            Nickname = user.nickname ?? string.Empty;
            Password = user.password ?? string.Empty;
            Status = Convert.ToByte(user.status);
            
            // 使用 await Task.Run 来避免构造函数中的异步调用
            Task.Run(async () => await LoadRolesForEditAsync(user.user_id)).Wait();
        }
        else
        {
            IsEditMode = false;
            // 使用 await Task.Run 来避免构造函数中的异步调用
            Task.Run(async () => await LoadRolesAsync()).Wait();
        }
    }
    
    private async Task LoadRolesAsync()
    {
        try
        {
            var roleList = await _userRepository.GetRolesAsync();
            Roles = new ObservableCollection<RoleCheckItem>(
                roleList.Select(r => new RoleCheckItem
                {
                    RoleId = r.role_id,
                    RoleName = r.role_name ?? string.Empty,
                    IsChecked = false
                })
            );
        }
        catch (Exception ex)
        {
            // 添加日志记录
            Debug.WriteLine($"LoadRoles error: {ex.Message}");
        }
    }

    private async Task LoadRolesForEditAsync(long userId)
    {
        try
        {
            var roleList = await _userRepository.GetRolesAsync();
            var userRoles = await _userRepository.GetUserRolesAsync(userId);

            Roles = new ObservableCollection<RoleCheckItem>(
                roleList.Select(r => new RoleCheckItem
                {
                    RoleId = r.role_id,
                    RoleName = r.role_name ?? string.Empty,
                    IsChecked = userRoles.Contains(r.role_id)
                })
            );
        }
        catch (Exception ex)
        {
            // 添加日志记录
            Debug.WriteLine($"LoadRolesForEdit error: {ex.Message}");
        }
    }
    
    private async void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(Username) || e.PropertyName == nameof(Password))
        {
            await ValidateProperty(e.PropertyName);
        }
    }
    
    private async Task ValidateProperty(string? propertyName)
    {
        var context = new ValidationContext<AddUserDialogViewModel>(this);
        var result = await _validator.ValidateAsync(context);

        if (propertyName == nameof(Username))
        {
            UsernameError = result.Errors
                .Where(e => e.PropertyName == nameof(Username))
                .Select(e => e.ErrorMessage)
                .FirstOrDefault() ?? string.Empty;
        }

        if (propertyName == nameof(Password))
        {
            PasswordError = result.Errors
                .Where(e => e.PropertyName == nameof(Password))
                .Select(e => e.ErrorMessage)
                .FirstOrDefault() ?? string.Empty;
        }
    }

    public async Task<bool> SaveAsync()
    {
        var validationResult = await _validator.ValidateAsync(this);
        if (!validationResult.IsValid)
            return false;

        var user = new SysUser
        {
            username = Username,
            password = Password,
            nickname = Nickname,
            status = Status,
            update_time = DateTime.Now
        };
        if (!IsEditMode)
        {
            user.password = Password;
            user.create_time = DateTime.Now;
        }
        else
        {
            user.user_id = _userId ?? 0;
        }

        var selectedRoleIds = Roles
            .Where(r => r.IsChecked)
            .Select(r => r.RoleId)
            .ToList();

        if (IsEditMode)
        {
            return await _userRepository.UpdateUserWithRolesAsync(user, selectedRoleIds) > 0;
        }
        
        return await _userRepository.InsertUserWithRolesAsync(user, selectedRoleIds) > 0;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var fluentValidationResult = _validator.Validate(this);
        return fluentValidationResult.Errors.Select(error => 
            new ValidationResult(error.ErrorMessage, new[] { error.PropertyName }));
    }
}

