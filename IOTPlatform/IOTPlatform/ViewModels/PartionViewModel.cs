using System;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FluentAvalonia.UI.Controls;
using IOTPlatform.Shared;
using IOTPlatform.Shared.Models;
using IOTPlatform.Shared.Services;
using Prism.Navigation.Regions;

namespace IOTPlatform.ViewModels;

public partial class PartionViewModel: ViewModelBase
{
    private readonly IRegionManager _regionManager;
    private readonly IMenuService _menuService;
    private readonly IUserService _userService;
    [ObservableProperty] private ObservableCollection<WMenuBase> _menus;
    public PartionViewModel(IRegionManager regionManager, IMenuService menuService, IUserService userService)
    {
        _regionManager = regionManager;
        _menuService = menuService;
        _userService = userService;
        Menus = new ObservableCollection<WMenuBase>();
    }
    public override void OnNavigatedTo(NavigationContext navigationContext)
    {
        base.OnNavigatedTo(navigationContext);
        string uri = navigationContext?.Parameters.GetValue<string>("Uri")!;
        string param = navigationContext?.Parameters.GetValue<string>("Param")!;
        Menus.Clear();
        LoadUserMenus();
    }
    
    private async void LoadUserMenus()
    {
        try
        {
            // 获取当前登录用户信息
            var currentUser = await _userService.GetCurrentUserAsync();
            // if (currentUser == null)
            // {
            //     return;
            // }

            // 获取用户的权限菜单
            var userMenus = await _userService.GetUserBackMenusAsync(currentUser.user_id);
            //var userMenus = await _userService.GetUserAllBackMenusAsync();
            // 清空并添加新的菜单项
            Menus.Clear();
            Menus.AddRange(userMenus);
        }
        catch (Exception ex)
        {
            // 处理异常
            System.Diagnostics.Debug.WriteLine($"加载用户菜单失败: {ex.Message}");
        }
    }
    
    [RelayCommand]
    private void SelectionChanged(NavigationViewSelectionChangedEventArgs? e)
    {
        if (e?.SelectedItem is WMenuItem menuItem)
        {
            _regionManager.RequestNavigate(RegionNames.PartionRegion, menuItem.NavigationTarget);
        }
    }
}