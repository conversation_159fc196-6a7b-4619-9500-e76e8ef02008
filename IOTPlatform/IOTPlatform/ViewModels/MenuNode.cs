using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using IOTPlatform.Shared;

namespace IOTPlatform.ViewModels;

public partial class MenuNode: ViewModelBase
{
    public string MenuId { get; set; } = string.Empty;
    public string? Title { get; set; } = string.Empty;

    [ObservableProperty]
    private bool _isChecked;

    partial void OnIsCheckedChanged(bool value)
    {
        foreach (var child in Children)
        {
            child.IsChecked = value;
        }
    }

    public ObservableCollection<MenuNode> Children { get; } = new();
}