using System;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using FluentAvalonia.UI.Controls;
using IOTPlatform.ORM.DbContext;
using IOTPlatform.ORM.Entity;
using IOTPlatform.ORM.Repository;
using IOTPlatform.Shared;
using IOTPlatform.Views;
using Prism.Commands;

namespace IOTPlatform.ViewModels;

public partial class UsersViewModel:ViewModelBase
{
    private SysUser? _selectedUser;
    private readonly UserRepository _userRepository;
    [ObservableProperty]
    private ObservableCollection<SysUser> _users;
    public SysUser? SelectedUser
    {
        get => _selectedUser;
        set
        {
            SetProperty(ref _selectedUser, value);
            OnPropertyChanged(nameof(IsUserSelected));
            EditCommand.RaiseCanExecuteChanged();
            DeleteCommand.RaiseCanExecuteChanged();
        }
    }

    public bool IsUserSelected => SelectedUser != null;

    public DelegateCommand AddCommand { get; }
    public DelegateCommand EditCommand { get; }
    public DelegateCommand DeleteCommand { get; }

    public UsersViewModel(UserRepository userRepository, DbContext db)
    {
        _userRepository = userRepository;
        AddCommand = new DelegateCommand(ExecuteAdd);
        EditCommand = new DelegateCommand(ExecuteEdit, () => IsUserSelected);
        DeleteCommand = new DelegateCommand(ExecuteDelete, () => IsUserSelected);
        Users = new ObservableCollection<SysUser>();
        InitializeAsync();
    }
    
    private async void InitializeAsync()
    {
        Users = new ObservableCollection<SysUser>(
            await _userRepository.GetUsersWithRolesAsync()
        );
    }

    private async void ExecuteAdd()
    {
        var dialogViewModel = new AddUserDialogViewModel(_userRepository);
        var dialog = new ContentDialog
        {
            Title = "新增用户",
            PrimaryButtonText = "保存",
            CloseButtonText = "取消",
            Content = new AddUserDialog { DataContext = dialogViewModel }
        };
        dialog.PrimaryButtonClick += (s, e) =>
        {
            if (!dialogViewModel.ValidateInput())
            {
                e.Cancel = true;
            }
        };
        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            if (await dialogViewModel.SaveAsync())
            {
                InitializeAsync();
            }
        }
    }

    private async void ExecuteEdit()
    {
        if (SelectedUser == null) return;
    
        var dialogViewModel = new AddUserDialogViewModel(_userRepository, SelectedUser);
        var dialog = new ContentDialog
        {
            Title = "编辑用户",
            PrimaryButtonText = "保存",
            CloseButtonText = "取消",
            Content = new AddUserDialog { DataContext = dialogViewModel }
        };
        dialog.PrimaryButtonClick += (s, e) =>
        {
            if (!dialogViewModel.ValidateInput())
            {
                e.Cancel = true;
            }
        };
        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            if (await dialogViewModel.SaveAsync())
            {
                InitializeAsync();
            }
        }
    }

    private async void ExecuteDelete()
    {
        if (SelectedUser == null) return;
        
        var dialog = new ContentDialog
        {
            Title = "删除确认",
            Content = $"确定要删除用户 {SelectedUser.username} 吗？",
            PrimaryButtonText = "删除",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Close
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            try
            {
                await _userRepository.DeleteAsync(SelectedUser.user_id);
                InitializeAsync();
            }
            catch (Exception ex)
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = $"删除用户失败：{ex.Message}",
                    CloseButtonText = "确定"
                };
                await errorDialog.ShowAsync();
            }
        }
    }
}