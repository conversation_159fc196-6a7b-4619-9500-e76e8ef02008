using System;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Threading;
using IOTPlatform.Shared;
using IOTPlatform.Shared.Services;
using IOTPlatform.Views;
using Prism.Ioc;
using Prism.Navigation.Regions;

namespace IOTPlatform.ViewModels;

public partial class MainWindowViewModel : ViewModelBase
{
    private readonly IRegionManager _regionManager;
    private readonly IMenuService _menuService;
    private readonly IContainerProvider _container;
    
    public MainWindowViewModel(IRegionManager regionManager, IMenuService menuService, IContainerProvider container) 
    {
        _regionManager = regionManager;
        _menuService = menuService;
        _container = container;
        Dispatcher.UIThread.Post(() => ShowLoginDialog(), DispatcherPriority.Loaded);
    }
    
    private async void ShowLoginDialog()
    {
        try
        {
            var mainWindow = GetMainWindow();
            if (mainWindow == null)
            {
                throw new InvalidOperationException("主窗口未初始化");
            }
            var loginView = _container.Resolve<LoginView>();
            var loginViewModel = _container.Resolve<LoginViewModel>();
            loginView.DataContext = loginViewModel;

            bool? result = await loginView.ShowDialog<bool>(mainWindow);

            if (result != true)
            {
                if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
                {
                    desktop.Shutdown();
                }
            }
            _regionManager.RegisterViewWithRegion(RegionNames.WindowRegion, typeof(MainView));
        }
        catch (Exception ex)
        {
            // 处理异常，可以显示错误消息或记录日志
            if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                desktop.Shutdown();
            }
        }

    }
    
    private Window GetMainWindow()
    {
        if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            return desktop.MainWindow!;
        }
        throw new InvalidOperationException("无法获取主窗口实例");
    }
}