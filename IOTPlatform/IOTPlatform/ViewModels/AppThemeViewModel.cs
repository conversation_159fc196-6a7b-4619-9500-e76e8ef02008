using System.Windows.Input;
using Avalonia;
using Avalonia.Styling;
using FluentAvalonia.Styling;
using IOTPlatform.Shared;
using Prism.Commands;

namespace IOTPlatform.ViewModels;

public class AppThemeViewModel : ViewModelBase
{
    private const string _system = "跟随系统";
    private const string _dark = "深色";
    private const string _light = "浅色";
    private readonly FluentAvaloniaTheme _faTheme;
    private string _currentAppTheme;

    public string[] AppThemes { get; } = new[] { _system, _light, _dark };

    public AppThemeViewModel()
    {
        _faTheme = App.Current.Styles[0] as FluentAvaloniaTheme;
        
        // 初始化当前主题
        var currentTheme = Application.Current.RequestedThemeVariant;
        _currentAppTheme = GetThemeString(currentTheme);

        SaveThemeCommand = new DelegateCommand(ExecuteSaveTheme);
        ResetThemeCommand = new DelegateCommand(ExecuteResetTheme);
    }
    
    private string GetThemeString(ThemeVariant? variant)
    {
        if (variant == null)
            return _system;
    
        if (variant == ThemeVariant.Dark)
            return _dark;
    
        if (variant == ThemeVariant.Light)
            return _light;
    
        return _system;
    }

    public string CurrentAppTheme
    {
        get => _currentAppTheme;
        set
        {
            if (SetProperty(ref _currentAppTheme, value))
            {
                UpdateTheme(value);
            }
        }
    }

    public ICommand SaveThemeCommand { get; }
    public ICommand ResetThemeCommand { get; }

    private void UpdateTheme(string themeName)
    {
        if (_faTheme == null) return;

        switch (themeName)
        {
            case _light:
                _faTheme.PreferSystemTheme = false;
                Application.Current.RequestedThemeVariant = ThemeVariant.Light;
                break;
            case _dark:
                _faTheme.PreferSystemTheme = false;
                Application.Current.RequestedThemeVariant = ThemeVariant.Dark;
                break;
            case _system:
                _faTheme.PreferSystemTheme = true;
                Application.Current.RequestedThemeVariant = null;
                break;
        }
    }

    private void ExecuteSaveTheme()
    {
        // 保存主题设置到配置文件或数据库
        // TODO: 实现保存逻辑
    }

    private void ExecuteResetTheme()
    {
        CurrentAppTheme = _system;
    }
}