using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using FluentValidation;
using IOTPlatform.ORM.Repository;
using IOTPlatform.Shared;
using IOTPlatform.Shared.Models;
using IOTPlatform.Shared.Services;
using IOTPlatform.Validator;

namespace IOTPlatform.ViewModels;

public partial class RoleMenuDialogViewModel : ViewModelBase, IValidatableObject
{
    private readonly IMenuService _menuService;
    private readonly RoleRepository _roleRepository;
    private readonly long _roleId;
    private readonly IValidator<RoleMenuDialogViewModel> _validator;
    private bool _hasValidationError;
    private string _validationMessage = string.Empty;
    
    [ObservableProperty]
    private ObservableCollection<MenuNode> _menuNodes = new();

    public bool HasValidationError
    {
        get => _hasValidationError;
        set => SetProperty(ref _hasValidationError, value);
    }

    public string ValidationMessage
    {
        get => _validationMessage;
        set => SetProperty(ref _validationMessage, value);
    }

    public bool ValidateSelection()
    {
        var hasSelectedMenus = MenuNodes.Any(node =>
            node.IsChecked || (node.Children?.Any(child => child.IsChecked) ?? false));

        HasValidationError = !hasSelectedMenus;
        ValidationMessage = hasSelectedMenus ? string.Empty : "请至少选择一个菜单权限";

        return hasSelectedMenus;
    }
    
    public RoleMenuDialogViewModel(IMenuService menuService, RoleRepository roleRepository, long roleId)
    {
        _menuService = menuService;
        _roleRepository = roleRepository;
        _roleId = roleId;
        _validator = new RoleMenuDialogValidator();
        
        InitializeMenus();
    }

    private async void InitializeMenus()
    {
        // 获取所有菜单
        var frontMenus = _menuService.GetMenuItems();
        var backMenus = _menuService.GetChildMenuItems();
        
        // 获取角色已有权限
        var roleMenus = await _roleRepository.GetRoleMenusAsync(_roleId);
        // 构建菜单树
        MenuNodes.Clear();
        foreach (var menu in frontMenus)
        {
            var parentNode = new MenuNode
            {
                MenuId = menu.MenuID,
                Title = menu.Title,
                IsChecked = roleMenus.Any(rm => rm.menu_id.ToString() == menu.MenuID)
            };
            var menuItem = menu as WMenuItem;
            if (menuItem?.MenuItems != null)
            {
                foreach (var subMenu in menuItem.MenuItems)
                {
                    parentNode.Children.Add(new MenuNode
                    {
                        MenuId = subMenu.MenuID,
                        Title = subMenu.Title,
                        IsChecked = roleMenus.Any(rm => rm.menu_id.ToString() == subMenu.MenuID)
                    });
                }
            }
            MenuNodes.Add(parentNode);
        }

        foreach (var menu in backMenus)
        {
            var parentNode = new MenuNode
            {
                MenuId = menu.MenuID,
                Title = menu.Title,
                IsChecked = roleMenus.Any(rm => rm.menu_id.ToString() == menu.MenuID)
            };

            var menuItem = menu as WMenuItem;
            if (menuItem?.MenuItems != null)
            {
                foreach (var subMenu in menuItem.MenuItems)
                {
                    parentNode.Children.Add(new MenuNode
                    {
                        MenuId = subMenu.MenuID,
                        Title = subMenu.Title,
                        IsChecked = roleMenus.Any(rm => rm.menu_id.ToString() == subMenu.MenuID)
                    });
                }
            }
            MenuNodes.Add(parentNode);
        }
    }

    public async Task SaveAsync()
    {
        var selectedMenuIds = GetSelectedMenuIds();
        await _roleRepository.UpdateRoleMenusAsync(_roleId, selectedMenuIds);
    }

    private List<string> GetSelectedMenuIds()
    {
        var selectedIds = new List<string>();
        foreach (var node in MenuNodes)
        {
            if (node.IsChecked)
            {
                selectedIds.Add(node.MenuId);
            }
            foreach (var child in node.Children)
            {
                if (child.IsChecked)
                {
                    selectedIds.Add(child.MenuId);
                }
            }
        }
        return selectedIds;
    }
    
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var fluentValidationResult = _validator.Validate(this);
        return fluentValidationResult.Errors.Select(error =>
            new ValidationResult(error.ErrorMessage, new[] { error.PropertyName }));
    }
}