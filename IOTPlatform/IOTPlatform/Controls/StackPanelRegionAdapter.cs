using Avalonia.Controls;
using Prism.Navigation.Regions;

namespace IOTPlatform.Controls;

public class StackPanelRegionAdapter : RegionAdapterBase<StackPanel>
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="regionBehaviorFactory"></param>
    public StackPanelRegionAdapter(IRegionBehaviorFactory regionBehaviorFactory) : base(regionBehaviorFactory)
    {
 
    }
 
    /// <summary>
    /// 重写适配器方法
    /// </summary>
    /// <param name="region"></param>
    /// <param name="regionTarget"></param>
    protected override void Adapt(IRegion region, StackPanel regionTarget)
    {
 
        region.Views.CollectionChanged += (s, e) =>
        {
            if (e.Action == System.Collections.Specialized.NotifyCollectionChangedAction.Add)
            {
                foreach (Control item in e.NewItems)
                {
                    regionTarget.Children.Add(item);
                }
            }
        };
    }
    /// <summary>
    /// 重写生成区域方法  直接返回new Region()即可
    /// </summary>
    /// <returns></returns>
    protected override IRegion CreateRegion()
    {
        return new Region();
    }
}