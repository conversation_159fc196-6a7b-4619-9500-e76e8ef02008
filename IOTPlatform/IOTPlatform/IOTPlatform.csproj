﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <LangVersion>latest</LangVersion>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    </PropertyGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia"/>
        <PackageReference Include="Avalonia.Themes.Fluent"/>
        <PackageReference Include="Avalonia.Fonts.Inter"/>
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Include="Avalonia.Diagnostics">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Avalonia.Xaml.Behaviors" />
        <PackageReference Include="CommunityToolkit.Mvvm"/>
        <PackageReference Include="FluentAvaloniaUI" />
        <PackageReference Include="FluentValidation" />
        <PackageReference Include="Microsoft.Extensions.Configuration" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
        <PackageReference Include="Prism.Core" />
        <PackageReference Include="Prism.DryIoc.Avalonia" />
        <PackageReference Include="Serilog" />
        <PackageReference Include="Serilog.Sinks.Console" />
        <PackageReference Include="Serilog.Sinks.File" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\IOTPlatform.InfluxDbWrapper\IOTPlatform.InfluxDbWrapper.csproj" />
      <ProjectReference Include="..\IOTPlatform.ORM\IOTPlatform.ORM.csproj" />
      <ProjectReference Include="..\IOTPlatform.Shared\IOTPlatform.Shared.csproj" />
    </ItemGroup>
</Project>
