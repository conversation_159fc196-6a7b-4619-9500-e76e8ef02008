using InfluxDB.Client.Core.Flux.Domain;

namespace IOTPlatform.InfluxDbWrapper.Services;

public interface IInfluxDbService
{
    Task WritePointAsync<T>(T point, string measurement = null) where T : class;
    Task WritePointsAsync<T>(IEnumerable<T> points, string measurement = null) where T : class;
    Task<IEnumerable<T>> QueryAsync<T>(string fluxQuery) where T : class;
    Task<IEnumerable<FluxRecord>> QueryRawAsync(string fluxQuery);
    Task<bool> TestConnectionAsync();
    Task DeleteAsync(string measurement, DateTime start, DateTime stop, string predicate = null);
}