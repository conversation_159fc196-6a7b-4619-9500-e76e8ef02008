using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Core.Flux.Domain;
using InfluxDB.Client.Writes;
using IOTPlatform.InfluxDbWrapper.Models;
using Serilog;

namespace IOTPlatform.InfluxDbWrapper.Services;

public class InfluxDbService : IInfluxDbService, IDisposable
{
    private readonly InfluxDBClient _client;
    private readonly InfluxDbConfiguration _config;
    private readonly ILogger _logger;
    private bool _disposed = false;

    public InfluxDbService(InfluxDbConfiguration config, ILogger logger)
    {
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger  ?? throw new ArgumentNullException(nameof(logger));

        var options = new InfluxDBClientOptions.Builder()
            .Url(_config.Url)
            .AuthenticateToken(_config.Token)
            .TimeOut(_config.Timeout)
            .Build();

        _client = InfluxDBClientFactory.Create(options);
    }

    public async Task WritePointAsync<T>(T point, string measurement = null) where T : class
    {
        try
        {
            var writeApi = _client.GetWriteApiAsync();

            var pointData = CreatePointData(point, measurement);
            await writeApi.WritePointAsync(pointData, _config.Bucket, _config.Organization);

            _logger?.Debug($"Successfully wrote point to measurement: {measurement ?? typeof(T).Name}");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, $"Failed to write point to measurement: {measurement ?? typeof(T).Name}");
            throw;
        }
    }

    public async Task WritePointsAsync<T>(IEnumerable<T> points, string measurement = null) where T : class
    {
        if (points == null || !points.Any())
            return;

        try
        {
            var writeApi = _client.GetWriteApiAsync();

            var pointDataList = points.Select(p => CreatePointData(p, measurement)).ToList();
            await writeApi.WritePointsAsync(pointDataList, _config.Bucket, _config.Organization);

            _logger?.Debug(
                $"Successfully wrote {pointDataList.Count} points to measurement: {measurement ?? typeof(T).Name}");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, $"Failed to write points to measurement: {measurement ?? typeof(T).Name}");
            throw;
        }
    }

    public async Task<IEnumerable<T>> QueryAsync<T>(string fluxQuery) where T : class
    {
        try
        {
            var queryApi = _client.GetQueryApi();
            var result = await queryApi.QueryAsync<T>(fluxQuery, _config.Organization);

            _logger?.Debug($"Successfully executed query, returned {result.Count()} records");
            return result;
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Failed to execute query");
            throw;
        }
    }

    public async Task<IEnumerable<FluxRecord>> QueryRawAsync(string fluxQuery)
    {
        try
        {
            var queryApi = _client.GetQueryApi();
            var fluxTables = await queryApi.QueryAsync(fluxQuery, _config.Organization);

            var records = fluxTables.SelectMany(table => table.Records).ToList();
            _logger?.Debug($"Successfully executed raw query, returned {records.Count} records");

            return records;
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Failed to execute raw query");
            throw;
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            var health = await _client.HealthAsync();
            var isConnected = health.Status == HealthCheck.StatusEnum.Pass;

            _logger?.Information($"Connection test result: {(isConnected ? "Success" : "Failed")}");
            return isConnected;
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, "Connection test failed");
            return false;
        }
    }

    public async Task DeleteAsync(string measurement, DateTime start, DateTime stop, string predicate = null)
    {
        try
        {
            var deleteApi = _client.GetDeleteApi();

            var deletePredicate = string.IsNullOrEmpty(predicate)
                ? $"_measurement=\"{measurement}\""
                : $"_measurement=\"{measurement}\" AND {predicate}";

            await deleteApi.Delete(start, stop, deletePredicate, _config.Bucket, _config.Organization);

            _logger?.Debug($"Successfully deleted data from measurement: {measurement}");
        }
        catch (Exception ex)
        {
            _logger?.Error(ex, $"Failed to delete data from measurement: {measurement}");
            throw;
        }
    }

    private PointData CreatePointData<T>(T obj, string measurement = null) where T : class
    {
        var measurementName = measurement ?? typeof(T).Name;
        var point = PointData.Measurement(measurementName);

        var properties = typeof(T).GetProperties();
        var timestamp = DateTime.UtcNow;

        foreach (var prop in properties)
        {
            var value = prop.GetValue(obj);
            if (value == null) continue;

            var propName = prop.Name.ToLower();

            // 处理时间戳字段
            if (propName == "timestamp" || propName == "time")
            {
                if (value is DateTime dt)
                    timestamp = dt;
                else if (value is DateTimeOffset dto)
                    timestamp = dto.DateTime;
                continue;
            }

            // 处理标签字段 (以Tag结尾的属性作为标签)
            if (propName.EndsWith("tag") || propName.EndsWith("tags"))
            {
                point = point.Tag(propName.Replace("tag", "").Replace("tags", ""), value.ToString());
                continue;
            }

            // 处理字段值
            switch (value)
            {
                case int i:
                    point = point.Field(propName, i);
                    break;
                case long l:
                    point = point.Field(propName, l);
                    break;
                case float f:
                    point = point.Field(propName, f);
                    break;
                case double d:
                    point = point.Field(propName, d);
                    break;
                case decimal dec:
                    point = point.Field(propName, (double)dec);
                    break;
                case bool b:
                    point = point.Field(propName, b);
                    break;
                case string s:
                    point = point.Field(propName, s);
                    break;
                default:
                    point = point.Field(propName, value.ToString());
                    break;
            }
        }

        return point.Timestamp(timestamp, WritePrecision.Ms);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _client?.Dispose();
            _disposed = true;
        }
    }
}